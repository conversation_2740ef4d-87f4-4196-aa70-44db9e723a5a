NPCCREATE
# generated by npcgen.perl which is made by ringo
#
#
# infile : data/npc/giiru/town/warp_g1_mura.gen
#line 5
{
floorid=300
borncorner=263,416,263,416
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5000|56|22
}
#line 6
{
floorid=300
borncorner=263,417,263,417
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5000|56|23
}
#line 7
{
floorid=300
borncorner=263,418,263,418
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5000|56|24
}
#line 10
{
floorid=5000
borncorner=57,22,57,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|264|416
}
#line 11
{
floorid=5000
borncorner=57,23,57,23
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|264|417
}
#line 12
{
floorid=5000
borncorner=57,24,57,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|264|418
}
#line 15
{
floorid=300
borncorner=234,427,234,427
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5000|29|33
}
#line 16
{
floorid=300
borncorner=234,428,234,428
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5000|29|34
}
#line 17
{
floorid=300
borncorner=234,429,234,429
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5000|29|35
}
#line 19
{
floorid=5000
borncorner=28,33,28,33
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|233|427
}
#line 20
{
floorid=5000
borncorner=28,34,28,34
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|233|428
}
#line 21
{
floorid=5000
borncorner=28,35,28,35
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|233|429
}
#line 26
{
floorid=300
borncorner=510,151,510,151
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5100|49|37
}
#line 27
{
floorid=300
borncorner=510,152,510,152
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5100|49|38
}
#line 28
{
floorid=300
borncorner=510,153,510,153
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5100|49|39
}
#line 30
{
floorid=5100
borncorner=50,37,50,37
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|511|151
}
#line 31
{
floorid=5100
borncorner=50,38,50,38
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|511|152
}
#line 32
{
floorid=5100
borncorner=50,39,50,39
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|511|153
}
#line 34
{
floorid=300
borncorner=498,167,498,167
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5100|38|52
}
#line 35
{
floorid=300
borncorner=499,167,499,167
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5100|39|52
}
#line 36
{
floorid=300
borncorner=500,167,500,167
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5100|40|52
}
#line 38
{
floorid=5100
borncorner=38,53,38,53
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|498|168
}
#line 39
{
floorid=5100
borncorner=39,53,39,53
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|499|168
}
#line 40
{
floorid=5100
borncorner=40,53,40,53
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|500|168
}
#line 42
{
floorid=5100
borncorner=43,31,43,31
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5105|0|22
}
# between
{
floorid=5105
borncorner=0,22,0,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5100|43|31
}
#line 43
{
floorid=5105
borncorner=0,23,0,23
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5100|43|31
}
#line 45
{
floorid=5100
borncorner=30,26,30,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5101|18|47
}
# between
{
floorid=5101
borncorner=18,47,18,47
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5100|30|26
}
#line 46
{
floorid=5101
borncorner=19,47,19,47
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5100|30|26
}
#line 47
{
floorid=5101
borncorner=40,49,40,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5100|33|26
}
#line 48
{
floorid=5100
borncorner=33,26,33,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5101|41|49
}
# between
{
floorid=5101
borncorner=41,49,41,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5100|33|26
}
#line 49
{
floorid=5101
borncorner=42,49,42,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5100|33|26
}
#line 51
{
floorid=5103
borncorner=22,29,22,29
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5100|39|29
}
#line 52
{
floorid=5100
borncorner=39,29,39,29
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5103|23|29
}
# between
{
floorid=5103
borncorner=23,29,23,29
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5100|39|29
}
#line 53
{
floorid=5103
borncorner=24,29,24,29
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5100|39|29
}
#line 54
{
floorid=5101
borncorner=49,36,49,36
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5103|0|21
}
# between
{
floorid=5103
borncorner=0,21,0,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5101|49|36
}
#line 55
{
floorid=5101
borncorner=49,37,49,37
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5103|0|22
}
# between
{
floorid=5103
borncorner=0,22,0,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5101|49|37
}
#line 57
{
floorid=5106
borncorner=15,49,15,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5100|22|31
}
#line 58
{
floorid=5100
borncorner=22,31,22,31
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5106|16|49
}
# between
{
floorid=5106
borncorner=16,49,16,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5100|22|31
}
#line 59
{
floorid=5106
borncorner=17,49,17,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5100|22|31
}
#line 61
{
floorid=5107
borncorner=0,6,0,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5106|26|4
}
#line 62
{
floorid=5107
borncorner=0,7,0,7
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5106|26|4
}
