NPCCREATE
# generated by npcgen.perl which is made by ringo
#
#
# infile : data/npc/seimu/dungeon/dungeon03.gen
#line 10
{
floorid=100
borncorner=666,235,666,235
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|31401|12|2
}
# between
{
floorid=31401
borncorner=12,2,12,2
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|100|666|235
}
#line 11
{
floorid=31401
borncorner=94,27,94,27
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|40|1|9
}
#line 15
{
floorid=200
borncorner=470,127,470,127
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|31501|6|112
}
# between
{
floorid=31501
borncorner=6,112,6,112
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|200|470|127
}
#line 16
{
floorid=31501
borncorner=34,2,34,2
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|40|11|18
}
#line 20
{
floorid=5000
borncorner=46,18,46,18
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|31301|10|10
}
# between
{
floorid=31301
borncorner=10,10,10,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5000|46|18
}
#line 21
{
floorid=31301
borncorner=10,103,10,103
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|40|12|1
}
#line 23
{
floorid=31301
borncorner=9,51,9,51
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|31302|52|11
}
# between
{
floorid=31302
borncorner=52,11,52,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|31301|9|51
}
#line 31
{
floorid=40
borncorner=6,3,6,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6000|32|68
}
# between
{
floorid=6000
borncorner=32,68,32,68
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|40|6|3
}
