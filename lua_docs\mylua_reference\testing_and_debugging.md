# mylua 系統測試和調試指南

## 🚨 挑戰分析

### 當前限制
1. **無法直接運行**: mylua 系統依賴完整的 Stone Age 服務器環境
2. **錯誤反饋延遲**: 只有部署到服務器才能發現問題
3. **調試困難**: 沒有傳統的 debugger 和 error stack trace
4. **測試成本高**: 每次測試都需要重啟服務器和創建測試環境

## 🛠️ 可行的測試策略

### 1. 靜態代碼分析

#### Lua 語法檢查
```bash
# 使用 luac 檢查語法錯誤
luac -p family_system.lua
luac -p familypool.lua

# 如果沒有輸出，表示語法正確
# 如果有錯誤，會顯示行號和錯誤信息
```

#### 自動化語法檢查腳本
```bash
#!/bin/bash
# check_lua_syntax.sh

echo "檢查 Lua 腳本語法..."
for file in data/ablua/npc/system/*.lua; do
    echo "檢查 $file"
    if luac -p "$file"; then
        echo "✅ $file 語法正確"
    else
        echo "❌ $file 語法錯誤"
        exit 1
    fi
done
echo "所有腳本語法檢查完成"
```

### 2. 模擬測試環境

#### 創建 mylua 函數模擬器
```lua
-- test_mock.lua - mylua 函數模擬器
local mock = {}

-- 模擬 char 模組
mock.char = {
    getInt = function(index, var_name)
        print(string.format("mock: char.getInt(%d, '%s')", index, var_name))
        if var_name == "家族索引" then return 1001 end
        if var_name == "家族地位" then return 3 end  -- FMMEMBER_LEADER
        return 0
    end,
    
    getChar = function(index, var_name)
        print(string.format("mock: char.getChar(%d, '%s')", index, var_name))
        if var_name == "家族" then return "測試家族" end
        return ""
    end,
    
    countitem = function(index, itemid)
        print(string.format("mock: char.countitem(%d, %d)", index, itemid))
        return 999  -- 模擬有足夠道具
    end,
    
    TalkToCli = function(index, npc_index, message, color)
        print(string.format("mock: TalkToCli - %s (%s)", message, color))
    end
}

-- 模擬 npc 模組
mock.npc = {
    CreateNpc = function(name, metamo, floor, x, y, dir)
        print(string.format("mock: CreateNpc('%s', %d, %d, %d, %d, %d)", 
              name, metamo, floor, x, y, dir))
        return 1000  -- 模擬成功創建的 NPC 索引
    end,
    
    DelItemNum = function(index, item_string)
        print(string.format("mock: DelItemNum(%d, '%s')", index, item_string))
        return 1  -- 模擬成功
    end,
    
    isFaceToFace = function(npc_index, player_index)
        print(string.format("mock: isFaceToFace(%d, %d)", npc_index, player_index))
        return 1  -- 模擬面對面
    end
}

-- 模擬 other 模組
mock.other = {
    CallFunction = function(func_name, file_path, args)
        print(string.format("mock: CallFunction('%s', '%s', %s)", 
              func_name, file_path, table.concat(args, ", ")))
        return true
    end,
    
    time = function()
        return os.time()
    end
}

-- 模擬 lssproto 模組
mock.lssproto = {
    windows = function(talkerindex, windowtype, buttontype, seqno, objindex, data)
        print(string.format("mock: lssproto.windows(%d, %d, %d, %d, %d, '%s')", 
              talkerindex, windowtype, buttontype, seqno, objindex, data or ""))
    end
}

-- 模擬 sasql 模組
mock.sasql = {
    query = function(sql, mode)
        print(string.format("mock: sasql.query('%s', %d)", sql, mode))
        return "mock_result"
    end
}

return mock
```

#### 單元測試框架
```lua
-- test_framework.lua
local mock = require("test_mock")

-- 設置全局模擬
char = mock.char
npc = mock.npc
other = mock.other
lssproto = mock.lssproto
sasql = mock.sasql

-- 測試框架
local TestFramework = {}

function TestFramework.run_test(test_name, test_func)
    print(string.format("\n🧪 運行測試: %s", test_name))
    print("=" .. string.rep("=", 50))
    
    local success, error_msg = pcall(test_func)
    
    if success then
        print(string.format("✅ 測試通過: %s", test_name))
    else
        print(string.format("❌ 測試失敗: %s", test_name))
        print(string.format("錯誤信息: %s", error_msg))
    end
    
    print("=" .. string.rep("=", 50))
end

function TestFramework.assert_equals(expected, actual, message)
    if expected ~= actual then
        error(string.format("%s - 期望: %s, 實際: %s", 
              message or "斷言失敗", tostring(expected), tostring(actual)))
    end
end

function TestFramework.assert_not_nil(value, message)
    if value == nil then
        error(message or "值不應該為 nil")
    end
end

return TestFramework
```

### 3. 功能測試案例

#### 家族系統測試
```lua
-- test_family_system.lua
local TestFramework = require("test_framework")

-- 載入要測試的模組 (需要先處理路徑和依賴)
-- dofile("family_system.lua")

-- 測試家族狀態檢查
TestFramework.run_test("測試家族狀態檢查", function()
    local talkerindex = 1001
    
    -- 模擬族長身份
    local family_status = char.getInt(talkerindex, "家族地位")
    local family_index = char.getInt(talkerindex, "家族索引")
    
    TestFramework.assert_equals(3, family_status, "應該是族長身份")
    TestFramework.assert_equals(1001, family_index, "家族索引應該正確")
    
    print("✅ 家族狀態檢查測試通過")
end)

-- 測試道具檢查
TestFramework.run_test("測試道具數量檢查", function()
    local talkerindex = 1001
    local diamond_count = char.countitem(talkerindex, 64)  -- 鑽石
    
    TestFramework.assert_not_nil(diamond_count, "道具數量不應該為 nil")
    print(string.format("鑽石數量: %d", diamond_count))
    
    print("✅ 道具檢查測試通過")
end)

-- 測試 NPC 創建
TestFramework.run_test("測試 NPC 創建", function()
    local npcindex = npc.CreateNpc("測試NPC", 101813, 1000, 100, 100, 6)
    
    TestFramework.assert_not_nil(npcindex, "NPC 索引不應該為 nil")
    TestFramework.assert_equals(1000, npcindex, "NPC 索引應該正確")
    
    print("✅ NPC 創建測試通過")
end)

print("🎯 開始運行家族系統測試...")
```

### 4. 集成測試策略

#### 分階段測試
```lua
-- integration_test.lua
local TestFramework = require("test_framework")

-- 階段 1: 基礎功能測試
TestFramework.run_test("階段1: 基礎函數調用", function()
    -- 測試所有 mylua 函數調用是否正確
    local talkerindex = 1001
    
    -- 測試家族變數獲取
    local family_status = char.getInt(talkerindex, "家族地位")
    local family_index = char.getInt(talkerindex, "家族索引")
    local family_name = char.getChar(talkerindex, "家族")
    
    -- 測試道具操作
    local diamond_count = char.countitem(talkerindex, 64)
    local result = npc.DelItemNum(talkerindex, "64,1")
    
    print("✅ 基礎函數調用測試完成")
end)

-- 階段 2: 業務邏輯測試
TestFramework.run_test("階段2: 業務邏輯驗證", function()
    -- 測試申請條件檢查邏輯
    local talkerindex = 1001
    
    -- 模擬申請條件檢查
    local family_status = char.getInt(talkerindex, "家族地位")
    local has_enough_items = char.countitem(talkerindex, 64) >= 800
    
    local can_apply = (family_status == 3) and has_enough_items
    
    TestFramework.assert_equals(true, can_apply, "應該滿足申請條件")
    
    print("✅ 業務邏輯測試完成")
end)

-- 階段 3: 跨文件調用測試
TestFramework.run_test("階段3: 跨文件調用", function()
    -- 測試 other.CallFunction 調用
    local result = other.CallFunction("openFamilyItemStorage", "./familypool.lua", {1001, 1000})
    
    TestFramework.assert_not_nil(result, "跨文件調用應該成功")
    
    print("✅ 跨文件調用測試完成")
end)
```

### 5. 服務器端調試技巧

#### 日誌記錄系統
```lua
-- debug_logger.lua
local Logger = {}

function Logger.log(level, message, ...)
    local timestamp = os.date("%Y-%m-%d %H:%M:%S")
    local formatted_message = string.format(message, ...)
    local log_entry = string.format("[%s] [%s] %s", timestamp, level, formatted_message)
    
    -- 寫入日誌文件 (如果支持)
    -- 或者使用遊戲內的日誌系統
    print(log_entry)
    
    -- 可以考慮寫入特定的日誌文件
    -- local file = io.open("family_system.log", "a")
    -- if file then
    --     file:write(log_entry .. "\n")
    --     file:close()
    -- end
end

function Logger.debug(message, ...)
    Logger.log("DEBUG", message, ...)
end

function Logger.info(message, ...)
    Logger.log("INFO", message, ...)
end

function Logger.error(message, ...)
    Logger.log("ERROR", message, ...)
end

return Logger
```

#### 在實際代碼中使用日誌
```lua
-- 在 family_system.lua 中
local Logger = require("debug_logger")

function Talked(meindex, talkerindex, szMes, color)
    Logger.debug("Talked 函數被調用: meindex=%d, talkerindex=%d", meindex, talkerindex)
    
    if npc.isFaceToFace(meindex, talkerindex) ~= 1 then 
        Logger.debug("玩家 %d 沒有面對 NPC %d", talkerindex, meindex)
        return 
    end
    
    local family_status = char.getInt(talkerindex, "家族地位")
    Logger.info("玩家 %d 的家族地位: %d", talkerindex, family_status)
    
    -- 其他邏輯...
end
```

### 6. 錯誤處理和容錯機制

#### 安全的函數調用包裝
```lua
-- safe_call.lua
local SafeCall = {}

function SafeCall.protected_call(func, error_message, ...)
    local success, result = pcall(func, ...)
    
    if not success then
        char.TalkToCli(talkerindex, -1, error_message or "系統錯誤，請聯繫管理員", "紅色")
        print(string.format("錯誤: %s", result))
        return nil
    end
    
    return result
end

function SafeCall.safe_char_getInt(talkerindex, var_name, default_value)
    return SafeCall.protected_call(
        function() return char.getInt(talkerindex, var_name) end,
        string.format("無法獲取變數 %s", var_name)
    ) or default_value or 0
end

function SafeCall.safe_database_query(sql, error_message)
    return SafeCall.protected_call(
        function() return sasql.query(sql, 1) end,
        error_message or "數據庫查詢失敗"
    )
end

return SafeCall
```

## 🎯 推薦的開發流程

### 1. 開發前準備
```bash
# 1. 語法檢查
luac -p family_system.lua

# 2. 運行模擬測試
lua test_family_system.lua

# 3. 檢查源碼引用
grep -r "char.getInt" mylua/charbase.c
```

### 2. 部署前驗證
```bash
# 1. 完整語法檢查
./check_lua_syntax.sh

# 2. 集成測試
lua integration_test.lua

# 3. 文件編碼檢查
file family_system.lua  # 確認是 Big5 編碼
```

### 3. 部署後監控
```lua
-- 在關鍵函數中添加日誌
Logger.info("家族建設申請: 玩家=%d, 家族=%d", talkerindex, family_index)

-- 添加錯誤捕獲
local success, error_msg = pcall(function()
    -- 關鍵邏輯
end)

if not success then
    Logger.error("家族建設申請失敗: %s", error_msg)
end
```

## 📋 測試檢查清單

### 部署前必檢項目
- [ ] Lua 語法檢查通過
- [ ] 模擬測試通過
- [ ] 所有 mylua 函數調用已驗證源碼
- [ ] 文件使用 Big5 編碼
- [ ] 添加了適當的錯誤處理
- [ ] 添加了調試日誌

### 部署後驗證項目
- [ ] NPC 成功創建
- [ ] 對話功能正常
- [ ] 窗口事件響應正常
- [ ] 數據庫操作成功
- [ ] 權限控制正確
- [ ] 錯誤處理生效

---

**版本**: 1.0  
**作者**: Augment Agent  
**最後更新**: 2025-06-18  
**說明**: mylua 系統的測試和調試策略指南
