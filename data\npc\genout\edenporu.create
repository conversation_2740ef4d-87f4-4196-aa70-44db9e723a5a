NPCCREATE
# generated by npcgen.perl which is made by ringo
#
#
# infile : data/npc/eden1/edenporu.gen
#line 4
{
floorid=5577
borncorner=13,24,13,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5540|500|647
}
#line 5
{
floorid=5577
borncorner=13,25,13,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5540|500|648
}
#line 7
{
floorid=5578
borncorner=13,24,13,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5540|500|647
}
#line 8
{
floorid=5578
borncorner=13,25,13,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5540|500|648
}
#line 10
{
floorid=5579
borncorner=14,23,14,23
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5578|31|18
}
#line 11
{
floorid=5579
borncorner=15,23,15,23
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5578|32|18
}
#line 14
{
floorid=5540
borncorner=599,469,599,469
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5570|0|55
}
# between
{
floorid=5570
borncorner=0,55,0,55
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5540|599|469
}
#line 15
{
floorid=5570
borncorner=0,56,0,56
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5540|599|469
}
#line 16
{
floorid=5570
borncorner=56,5,56,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5571|30|53
}
# between
{
floorid=5571
borncorner=30,53,30,53
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5570|56|5
}
#line 17
{
floorid=5571
borncorner=54,42,54,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5572|16|84
}
# between
{
floorid=5572
borncorner=16,84,16,84
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5571|54|42
}
#line 18
{
floorid=5572
borncorner=27,15,27,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5573|14|191
}
# between
{
floorid=5573
borncorner=14,191,14,191
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5572|27|15
}
#line 19
{
floorid=5573
borncorner=11,6,11,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5574|44|89
}
# between
{
floorid=5574
borncorner=44,89,44,89
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5573|11|6
}
#line 20
{
floorid=5574
borncorner=16,19,16,19
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5575|13|54
}
# between
{
floorid=5575
borncorner=13,54,13,54
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5574|16|19
}
#line 22
{
floorid=5575
borncorner=6,4,6,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5576|18|60
}
# between
{
floorid=5576
borncorner=18,60,18,60
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5575|6|4
}
#line 23
{
floorid=5582
borncorner=18,60,18,60
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5575|6|4
}
#line 24
{
floorid=5580
borncorner=18,60,18,60
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5575|6|4
}
#line 26
{
floorid=5576
borncorner=26,85,26,85
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 27
{
floorid=5576
borncorner=26,86,26,86
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 28
{
floorid=5576
borncorner=26,87,26,87
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 29
{
floorid=5576
borncorner=26,88,26,88
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 30
{
floorid=5576
borncorner=26,89,26,89
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 32
{
floorid=5576
borncorner=30,86,30,86
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 33
{
floorid=5576
borncorner=30,87,30,87
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 34
{
floorid=5576
borncorner=30,88,30,88
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 35
{
floorid=5576
borncorner=30,89,30,89
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
