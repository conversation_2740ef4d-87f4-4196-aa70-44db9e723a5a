NPCCREATE
# generated by npcgen.perl which is made by ringo
#
#
# infile : data/npc/sainasu/dungeon/dungeon00.gen
#line 8
{
floorid=100
borncorner=191,365,191,365
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10001|40|3
}
# between
{
floorid=10001
borncorner=40,3,40,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|100|191|365
}
#line 9
{
floorid=10001
borncorner=25,42,25,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10002|16|41
}
# between
{
floorid=10002
borncorner=16,41,16,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10001|25|42
}
#line 10
{
floorid=10002
borncorner=8,18,8,18
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10003|26|25
}
# between
{
floorid=10003
borncorner=26,25,26,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10002|8|18
}
#line 11
{
floorid=10003
borncorner=36,3,36,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10004|26|25
}
# between
{
floorid=10004
borncorner=26,25,26,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10003|36|3
}
#line 12
{
floorid=10004
borncorner=24,33,24,33
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10005|47|23
}
# between
{
floorid=10005
borncorner=47,23,47,23
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10004|24|33
}
#line 13
{
floorid=10005
borncorner=19,26,19,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10006|17|3
}
# between
{
floorid=10006
borncorner=17,3,17,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10005|19|26
}
#line 14
{
floorid=10006
borncorner=14,91,14,91
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10007|41|48
}
# between
{
floorid=10007
borncorner=41,48,41,48
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10006|14|91
}
#line 15
{
floorid=10007
borncorner=34,8,34,8
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|100|191|425
}
#line 23
{
floorid=100
borncorner=318,428,318,428
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10101|14|49
}
# between
{
floorid=10101
borncorner=14,49,14,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|100|318|428
}
#line 24
{
floorid=10101
borncorner=41,40,41,40
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10102|46|44
}
# between
{
floorid=10102
borncorner=46,44,46,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10101|41|40
}
#line 25
{
floorid=10102
borncorner=13,1,13,1
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10103|13|8
}
# between
{
floorid=10103
borncorner=13,8,13,8
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10102|13|1
}
#line 26
{
floorid=10103
borncorner=43,49,43,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1200|49|26
}
# between
{
floorid=1200
borncorner=49,26,49,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10103|43|49
}
#line 34
{
floorid=1100
borncorner=96,62,96,62
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10201|6|4
}
# between
{
floorid=10201
borncorner=6,4,6,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1100|96|62
}
#line 35
{
floorid=10201
borncorner=18,48,18,48
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10202|9|7
}
# between
{
floorid=10202
borncorner=9,7,9,7
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10201|18|48
}
#line 36
{
floorid=10202
borncorner=33,49,33,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10203|8|6
}
# between
{
floorid=10203
borncorner=8,6,8,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10202|33|49
}
#line 37
{
floorid=10204
borncorner=0,6,0,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10202|31|15
}
#line 38
{
floorid=10204
borncorner=0,7,0,7
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10202|31|15
}
#line 40
{
floorid=200
borncorner=174,402,174,402
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10203|31|46
}
# between
{
floorid=10203
borncorner=31,46,31,46
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|200|174|402
}
#line 49
{
floorid=10301
borncorner=6,43,6,43
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10302|27|6
}
# between
{
floorid=10302
borncorner=27,6,27,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10301|6|43
}
#line 50
{
floorid=10302
borncorner=6,29,6,29
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10303|7|24
}
# between
{
floorid=10303
borncorner=7,24,7,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10302|6|29
}
#line 51
{
floorid=10302
borncorner=29,36,29,36
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10303|25|33
}
# between
{
floorid=10303
borncorner=25,33,25,33
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10302|29|36
}
#line 52
{
floorid=10302
borncorner=15,37,15,37
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10303|7|41
}
# between
{
floorid=10303
borncorner=7,41,7,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10302|15|37
}
#line 53
{
floorid=10303
borncorner=14,7,14,7
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10304|14|7
}
# between
{
floorid=10304
borncorner=14,7,14,7
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10303|14|7
}
#line 54
{
floorid=10303
borncorner=45,4,45,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10304|44|04
}
# between
{
floorid=10304
borncorner=44,04,44,04
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10303|45|4
}
#line 55
{
floorid=10304
borncorner=21,37,21,37
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10305|37|6
}
# between
{
floorid=10305
borncorner=37,6,37,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10304|21|37
}
#line 56
{
floorid=10305
borncorner=18,38,18,38
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10306|19|39
}
# between
{
floorid=10306
borncorner=19,39,19,39
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10305|18|38
}
#line 57
{
floorid=10305
borncorner=9,5,9,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10306|9|6
}
# between
{
floorid=10306
borncorner=9,6,9,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10305|9|5
}
#line 58
{
floorid=10305
borncorner=24,11,24,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10306|23|11
}
# between
{
floorid=10306
borncorner=23,11,23,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10305|24|11
}
#line 59
{
floorid=10305
borncorner=5,24,5,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10306|5|26
}
# between
{
floorid=10306
borncorner=5,26,5,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10305|5|24
}
#line 60
{
floorid=10306
borncorner=38,6,38,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10307|37|6
}
# between
{
floorid=10307
borncorner=37,6,37,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10306|38|6
}
#line 61
{
floorid=10306
borncorner=42,41,42,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10307|39|41
}
# between
{
floorid=10307
borncorner=39,41,39,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10306|42|41
}
#line 62
{
floorid=10307
borncorner=13,19,13,19
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10308|12|5
}
# between
{
floorid=10308
borncorner=12,5,12,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10307|13|19
}
#line 63
{
floorid=10307
borncorner=18,17,18,17
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10308|20|5
}
# between
{
floorid=10308
borncorner=20,5,20,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10307|18|17
}
#line 64
{
floorid=10307
borncorner=22,17,22,17
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10308|28|4
}
# between
{
floorid=10308
borncorner=28,4,28,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10307|22|17
}
#line 65
{
floorid=10307
borncorner=27,18,27,18
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10308|40|5
}
# between
{
floorid=10308
borncorner=40,5,40,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10307|27|18
}
#line 72
{
floorid=10601
borncorner=27,49,27,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|100|411|339
}
#line 73
{
floorid=10601
borncorner=28,49,28,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|100|411|339
}
#line 74
{
floorid=10601
borncorner=29,49,29,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|100|411|339
}
#line 75
{
floorid=10601
borncorner=30,49,30,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|100|411|339
}
#line 76
{
floorid=10601
borncorner=45,43,45,43
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10602|7|41
}
# between
{
floorid=10602
borncorner=7,41,7,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10601|45|43
}
#line 77
{
floorid=10602
borncorner=5,4,5,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10603|7|3
}
# between
{
floorid=10603
borncorner=7,3,7,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10602|5|4
}
#line 78
{
floorid=10603
borncorner=7,45,7,45
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10604|4|3
}
# between
{
floorid=10604
borncorner=4,3,4,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10603|7|45
}
#line 79
{
floorid=10604
borncorner=4,34,4,34
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10605|11|4
}
# between
{
floorid=10605
borncorner=11,4,11,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10604|4|34
}
#line 97
{
floorid=10801
borncorner=7,5,7,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10802|16|15
}
# between
{
floorid=10802
borncorner=16,15,16,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10801|7|5
}
#line 98
{
floorid=10801
borncorner=40,5,40,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10803|8|2
}
# between
{
floorid=10803
borncorner=8,2,8,2
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10801|40|5
}
#line 99
{
floorid=10803
borncorner=7,37,7,37
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10804|43|44
}
# between
{
floorid=10804
borncorner=43,44,43,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10803|7|37
}
#line 100
{
floorid=10804
borncorner=46,7,46,7
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10805|42|6
}
# between
{
floorid=10805
borncorner=42,6,42,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10804|46|7
}
#line 101
{
floorid=10805
borncorner=12,35,12,35
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10806|6|28
}
# between
{
floorid=10806
borncorner=6,28,6,28
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10805|12|35
}
#line 102
{
floorid=10806
borncorner=57,5,57,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10807|47|10
}
# between
{
floorid=10807
borncorner=47,10,47,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10806|57|5
}
#line 103
{
floorid=10807
borncorner=18,45,18,45
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10808|36|54
}
# between
{
floorid=10808
borncorner=36,54,36,54
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10807|18|45
}
#line 104
{
floorid=10808
borncorner=4,4,4,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10809|7|3
}
# between
{
floorid=10809
borncorner=7,3,7,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10808|4|4
}
#line 112
{
floorid=100
borncorner=237,445,237,445
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10901|25|49
}
# between
{
floorid=10901
borncorner=25,49,25,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|100|237|445
}
#line 113
{
floorid=100
borncorner=237,445,237,445
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10901|26|49
}
# between
{
floorid=10901
borncorner=26,49,26,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|100|237|445
}
#line 117
{
floorid=10902
borncorner=46,25,46,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10903|27|43
}
# between
{
floorid=10903
borncorner=27,43,27,43
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10902|46|25
}
#line 118
{
floorid=10903
borncorner=17,24,17,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10904|26|48
}
# between
{
floorid=10904
borncorner=26,48,26,48
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10903|17|24
}
#line 119
{
floorid=10904
borncorner=36,20,36,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10905|46|4
}
# between
{
floorid=10905
borncorner=46,4,46,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10904|36|20
}
#line 120
{
floorid=10905
borncorner=4,47,4,47
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10906|11|4
}
# between
{
floorid=10906
borncorner=11,4,11,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10905|4|47
}
#line 121
{
floorid=10906
borncorner=16,57,16,57
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10907|12|58
}
# between
{
floorid=10907
borncorner=12,58,12,58
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10906|16|57
}
#line 122
{
floorid=10907
borncorner=13,1,13,1
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10908|46|16
}
# between
{
floorid=10908
borncorner=46,16,46,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10907|13|1
}
#line 123
{
floorid=10908
borncorner=8,26,8,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10909|15|70
}
# between
{
floorid=10909
borncorner=15,70,15,70
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10908|8|26
}
#line 124
{
floorid=10909
borncorner=8,4,8,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10910|4|3
}
# between
{
floorid=10910
borncorner=4,3,4,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10909|8|4
}
#line 125
{
floorid=10909
borncorner=66,49,66,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10910|66|30
}
# between
{
floorid=10910
borncorner=66,30,66,30
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10909|66|49
}
#line 126
{
floorid=10910
borncorner=7,67,7,67
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10911|39|45
}
# between
{
floorid=10911
borncorner=39,45,39,45
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10910|7|67
}
#line 129
{
floorid=10901
borncorner=46,4,46,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10912|6|3
}
# between
{
floorid=10912
borncorner=6,3,6,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10901|46|4
}
#line 130
{
floorid=10912
borncorner=23,34,23,34
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10913|7|21
}
# between
{
floorid=10913
borncorner=7,21,7,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10912|23|34
}
#line 131
{
floorid=10913
borncorner=35,31,35,31
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10914|17|53
}
# between
{
floorid=10914
borncorner=17,53,17,53
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10913|35|31
}
#line 132
{
floorid=10914
borncorner=7,4,7,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10915|6|7
}
# between
{
floorid=10915
borncorner=6,7,6,7
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10914|7|4
}
#line 133
{
floorid=10915
borncorner=51,31,51,31
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10916|16|4
}
# between
{
floorid=10916
borncorner=16,4,16,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10915|51|31
}
#line 134
{
floorid=10916
borncorner=16,72,16,72
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10917|19|4
}
# between
{
floorid=10917
borncorner=19,4,19,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10916|16|72
}
#line 135
{
floorid=10917
borncorner=84,25,84,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10918|25|22
}
# between
{
floorid=10918
borncorner=25,22,25,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10917|84|25
}
#line 147
{
floorid=100
borncorner=328,633,328,633
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11001|0|43
}
# between
{
floorid=11001
borncorner=0,43,0,43
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|100|328|633
}
#line 148
{
floorid=100
borncorner=328,633,328,633
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11001|0|44
}
# between
{
floorid=11001
borncorner=0,44,0,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|100|328|633
}
#line 149
{
floorid=11001
borncorner=44,5,44,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11002|46|42
}
# between
{
floorid=11002
borncorner=46,42,46,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11001|44|5
}
#line 150
{
floorid=11002
borncorner=7,6,7,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11003|42|3
}
# between
{
floorid=11003
borncorner=42,3,42,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11002|7|6
}
#line 151
{
floorid=11003
borncorner=6,5,6,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11004|7|5
}
# between
{
floorid=11004
borncorner=7,5,7,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11003|6|5
}
#line 152
{
floorid=11004
borncorner=45,3,45,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11005|45|30
}
# between
{
floorid=11005
borncorner=45,30,45,30
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11004|45|3
}
#line 153
{
floorid=11005
borncorner=7,27,7,27
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11006|43|3
}
# between
{
floorid=11006
borncorner=43,3,43,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11005|7|27
}
#line 167
{
floorid=11301
borncorner=12,41,12,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11302|12|41
}
# between
{
floorid=11302
borncorner=12,41,12,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11301|12|41
}
#line 168
{
floorid=11301
borncorner=8,4,8,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11302|8|4
}
# between
{
floorid=11302
borncorner=8,4,8,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11301|8|4
}
#line 169
{
floorid=11301
borncorner=41,4,41,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11302|41|4
}
# between
{
floorid=11302
borncorner=41,4,41,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11301|41|4
}
#line 170
{
floorid=11301
borncorner=45,41,45,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11302|45|41
}
# between
{
floorid=11302
borncorner=45,41,45,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11301|45|41
}
#line 172
{
floorid=11302
borncorner=23,20,23,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11303|45|44
}
# between
{
floorid=11303
borncorner=45,44,45,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11302|23|20
}
#line 174
{
floorid=11303
borncorner=18,44,18,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11304|54|44
}
# between
{
floorid=11304
borncorner=54,44,54,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11303|18|44
}
