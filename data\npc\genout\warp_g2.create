NPCCREATE
# generated by npcgen.perl which is made by ringo
#
#
# infile : data/npc/giiru/dungeon/warp_g2.gen
#line 7
{
floorid=30101
borncorner=8,99,8,99
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|393|245
}
#line 8
{
floorid=300
borncorner=393,245,393,245
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30101|9|99
}
# between
{
floorid=30101
borncorner=9,99,9,99
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|393|245
}
#line 9
{
floorid=30101
borncorner=10,99,10,99
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|393|245
}
#line 10
{
floorid=30101
borncorner=40,59,40,59
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30102|12|19
}
# between
{
floorid=30102
borncorner=12,19,12,19
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30101|40|59
}
#line 11
{
floorid=30101
borncorner=87,44,87,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30103|7|37
}
# between
{
floorid=30103
borncorner=7,37,7,37
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30101|87|44
}
#line 12
{
floorid=30101
borncorner=73,7,73,7
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30107|36|50
}
# between
{
floorid=30107
borncorner=36,50,36,50
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30101|73|7
}
#line 13
{
floorid=30103
borncorner=34,47,34,47
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30104|12|5
}
# between
{
floorid=30104
borncorner=12,5,12,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30103|34|47
}
#line 14
{
floorid=30103
borncorner=49,6,49,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30105|20|15
}
# between
{
floorid=30105
borncorner=20,15,20,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30103|49|6
}
#line 15
{
floorid=30103
borncorner=59,62,59,62
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30106|15|23
}
# between
{
floorid=30106
borncorner=15,23,15,23
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30103|59|62
}
#line 16
{
floorid=30104
borncorner=17,49,17,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|434|250
}
#line 17
{
floorid=30104
borncorner=18,49,18,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|434|250
}
# between
{
floorid=300
borncorner=434,250,434,250
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30104|18|49
}
#line 18
{
floorid=30105
borncorner=0,6,0,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|452|223
}
# between
{
floorid=300
borncorner=452,223,452,223
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30105|0|6
}
#line 19
{
floorid=30105
borncorner=0,7,0,7
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|452|223
}
#line 20
{
floorid=30106
borncorner=31,49,31,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|467|268
}
#line 21
{
floorid=30106
borncorner=32,49,32,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|467|268
}
# between
{
floorid=300
borncorner=467,268,467,268
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30106|32|49
}
#line 22
{
floorid=30107
borncorner=34,30,34,30
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30108|26|10
}
# between
{
floorid=30108
borncorner=26,10,26,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30107|34|30
}
#line 23
{
floorid=30108
borncorner=0,10,0,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|431|197
}
# between
{
floorid=300
borncorner=431,197,431,197
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30108|0|10
}
#line 24
{
floorid=30108
borncorner=0,11,0,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|431|197
}
#line 28
{
floorid=300
borncorner=398,183,398,183
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30201|42|25
}
# between
{
floorid=30201
borncorner=42,25,42,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|398|183
}
#line 29
{
floorid=30201
borncorner=11,2,11,2
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30202|45|19
}
# between
{
floorid=30202
borncorner=45,19,45,19
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30201|11|2
}
#line 30
{
floorid=30202
borncorner=11,2,11,2
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30203|55|40
}
# between
{
floorid=30203
borncorner=55,40,55,40
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30202|11|2
}
#line 31
{
floorid=30203
borncorner=50,65,50,65
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30204|7|42
}
# between
{
floorid=30204
borncorner=7,42,7,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30203|50|65
}
#line 32
{
floorid=30204
borncorner=13,6,13,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30205|39|9
}
# between
{
floorid=30205
borncorner=39,9,39,9
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30204|13|6
}
#line 33
{
floorid=30205
borncorner=10,4,10,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|272|134
}
# between
{
floorid=300
borncorner=272,134,272,134
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30205|10|4
}
#line 36
{
floorid=300
borncorner=247,222,247,222
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30301|23|37
}
# between
{
floorid=30301
borncorner=23,37,23,37
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|247|222
}
#line 37
{
floorid=30301
borncorner=40,3,40,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30302|27|23
}
# between
{
floorid=30302
borncorner=27,23,27,23
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30301|40|3
}
#line 38
{
floorid=30302
borncorner=16,3,16,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30303|30|40
}
# between
{
floorid=30303
borncorner=30,40,30,40
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30302|16|3
}
#line 39
{
floorid=30303
borncorner=47,19,47,19
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30304|30|40
}
# between
{
floorid=30304
borncorner=30,40,30,40
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30303|47|19
}
#line 40
{
floorid=30304
borncorner=37,11,37,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30305|50|52
}
# between
{
floorid=30305
borncorner=50,52,50,52
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30304|37|11
}
#line 41
{
floorid=30305
borncorner=33,48,33,48
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30306|44|37
}
# between
{
floorid=30306
borncorner=44,37,44,37
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30305|33|48
}
#line 42
{
floorid=30305
borncorner=34,18,34,18
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30306|4|41
}
# between
{
floorid=30306
borncorner=4,41,4,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30305|34|18
}
#line 43
{
floorid=30305
borncorner=36,5,36,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30306|40|3
}
# between
{
floorid=30306
borncorner=40,3,40,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30305|36|5
}
#line 44
{
floorid=30306
borncorner=16,3,16,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30307|27|31
}
# between
{
floorid=30307
borncorner=27,31,27,31
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30306|16|3
}
#line 45
{
floorid=30307
borncorner=10,6,10,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|182|162
}
# between
{
floorid=300
borncorner=182,162,182,162
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30307|10|6
}
#line 48
{
floorid=300
borncorner=275,149,275,149
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30401|15|3
}
# between
{
floorid=30401
borncorner=15,3,15,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|275|149
}
#line 49
{
floorid=30401
borncorner=42,41,42,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30402|26|34
}
# between
{
floorid=30402
borncorner=26,34,26,34
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30401|42|41
}
#line 50
{
floorid=30402
borncorner=42,16,42,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30403|13|3
}
# between
{
floorid=30403
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30402|42|16
}
#line 51
{
floorid=30403
borncorner=26,39,26,39
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|289|174
}
# between
{
floorid=300
borncorner=289,174,289,174
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30403|26|39
}
#line 54
{
floorid=300
borncorner=295,259,295,259
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30505|8|49
}
# between
{
floorid=30505
borncorner=8,49,8,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|295|259
}
#line 55
{
floorid=30505
borncorner=7,2,7,2
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30506|10|5
}
# between
{
floorid=30506
borncorner=10,5,10,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30505|7|2
}
#line 56
{
floorid=30505
borncorner=35,6,35,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30506|29|8
}
# between
{
floorid=30506
borncorner=29,8,29,8
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30505|35|6
}
#line 57
{
floorid=30505
borncorner=44,21,44,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30506|45|18
}
# between
{
floorid=30506
borncorner=45,18,45,18
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30505|44|21
}
#line 58
{
floorid=30505
borncorner=45,30,45,30
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30506|42|32
}
# between
{
floorid=30506
borncorner=42,32,42,32
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30505|45|30
}
#line 59
{
floorid=30506
borncorner=33,26,33,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30507|13|10
}
# between
{
floorid=30507
borncorner=13,10,13,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30506|33|26
}
#line 60
{
floorid=30507
borncorner=34,43,34,43
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30508|14|2
}
# between
{
floorid=30508
borncorner=14,2,14,2
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30507|34|43
}
#line 61
{
floorid=30508
borncorner=41,41,41,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30509|15|3
}
# between
{
floorid=30509
borncorner=15,3,15,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30508|41|41
}
#line 62
{
floorid=30509
borncorner=10,40,10,40
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30510|8|32
}
# between
{
floorid=30510
borncorner=8,32,8,32
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30509|10|40
}
#line 63
{
floorid=30510
borncorner=46,17,46,17
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30509|59|26
}
# between
{
floorid=30509
borncorner=59,26,59,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30510|46|17
}
#line 64
{
floorid=30509
borncorner=97,12,97,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30511|9|3
}
# between
{
floorid=30511
borncorner=9,3,9,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30509|97|12
}
#line 65
{
floorid=30511
borncorner=44,32,44,32
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30502|16|3
}
# between
{
floorid=30502
borncorner=16,3,16,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30511|44|32
}
#line 66
{
floorid=300
borncorner=383,299,383,299
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30501|15|2
}
# between
{
floorid=30501
borncorner=15,2,15,2
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|383|299
}
#line 67
{
floorid=30501
borncorner=33,35,33,35
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30502|31|30
}
# between
{
floorid=30502
borncorner=31,30,31,30
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30501|33|35
}
#line 69
{
floorid=30503
borncorner=26,34,26,34
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30504|22|5
}
# between
{
floorid=30504
borncorner=22,5,22,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30503|26|34
}
#line 70
{
floorid=30504
borncorner=22,19,22,19
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|388|337
}
# between
{
floorid=300
borncorner=388,337,388,337
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30504|22|19
}
#line 74
{
floorid=300
borncorner=266,294,266,294
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30600|15|21
}
# between
{
floorid=30600
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|266|294
}
#line 75
{
floorid=300
borncorner=267,294,267,294
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30600|16|21
}
# between
{
floorid=30600
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|267|294
}
#line 76
{
floorid=30600
borncorner=14,6,14,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|266|287
}
# between
{
floorid=300
borncorner=266,287,266,287
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30600|14|6
}
#line 80
{
floorid=30601
borncorner=11,58,11,58
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30602|95|54
}
# between
{
floorid=30602
borncorner=95,54,95,54
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30601|11|58
}
#line 81
{
floorid=30602
borncorner=47,60,47,60
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30603|77|78
}
# between
{
floorid=30603
borncorner=77,78,77,78
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30602|47|60
}
#line 82
{
floorid=30603
borncorner=48,56,48,56
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30604|22|28
}
# between
{
floorid=30604
borncorner=22,28,22,28
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30603|48|56
}
#line 85
{
floorid=300
borncorner=122,170,122,170
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30701|14|6
}
# between
{
floorid=30701
borncorner=14,6,14,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|122|170
}
#line 86
{
floorid=30701
borncorner=55,43,55,43
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30702|19|13
}
# between
{
floorid=30702
borncorner=19,13,19,13
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30701|55|43
}
#line 87
{
floorid=30702
borncorner=43,35,43,35
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30703|43|5
}
# between
{
floorid=30703
borncorner=43,5,43,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30702|43|35
}
#line 88
{
floorid=30703
borncorner=15,44,15,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|157|209
}
# between
{
floorid=300
borncorner=157,209,157,209
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30703|15|44
}
