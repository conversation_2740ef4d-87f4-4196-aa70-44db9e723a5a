NPCCREATE
# generated by npcgen.perl which is made by ringo
#
#
# infile : data/npc/extra/dungeon/cyatroom.gen
#line 7
{
floorid=110
borncorner=6,1,6,1
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|113|4|10
}
# between
{
floorid=113
borncorner=4,10,4,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|110|6|1
}
#line 8
{
floorid=110
borncorner=7,1,7,1
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|113|5|10
}
# between
{
floorid=113
borncorner=5,10,5,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|110|7|1
}
#line 11
{
floorid=110
borncorner=12,1,12,1
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|114|4|10
}
# between
{
floorid=114
borncorner=4,10,4,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|110|12|1
}
#line 12
{
floorid=110
borncorner=13,1,13,1
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|114|5|10
}
# between
{
floorid=114
borncorner=5,10,5,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|110|13|1
}
#line 15
{
floorid=110
borncorner=18,1,18,1
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|115|4|10
}
# between
{
floorid=115
borncorner=4,10,4,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|110|18|1
}
#line 16
{
floorid=110
borncorner=19,1,19,1
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|115|5|10
}
# between
{
floorid=115
borncorner=5,10,5,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|110|19|1
}
#line 19
{
floorid=110
borncorner=24,1,24,1
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|111|12|21
}
# between
{
floorid=111
borncorner=12,21,12,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|110|24|1
}
#line 20
{
floorid=110
borncorner=25,1,25,1
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|111|13|21
}
# between
{
floorid=111
borncorner=13,21,13,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|110|25|1
}
#line 23
{
floorid=110
borncorner=30,1,30,1
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|112|5|19
}
# between
{
floorid=112
borncorner=5,19,5,19
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|110|30|1
}
#line 24
{
floorid=110
borncorner=31,1,31,1
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|112|5|19
}
#line 27
{
floorid=110
borncorner=36,1,36,1
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|116|23|40
}
# between
{
floorid=116
borncorner=23,40,23,40
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|110|36|1
}
#line 28
{
floorid=110
borncorner=37,1,37,1
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|116|23|40
}
#line 29
{
floorid=116
borncorner=22,40,22,40
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|110|36|1
}
#line 30
{
floorid=116
borncorner=24,40,24,40
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|110|36|1
}
