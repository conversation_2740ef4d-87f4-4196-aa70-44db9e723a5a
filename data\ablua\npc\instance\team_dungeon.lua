--=============================================
-- 五人組隊副本系統 (Team Dungeon System)
-- 功能: 需要五人組隊挑戰，具有CD時間限制
-- 作者: Claude <PERSON> Assistant
-- 版本: 3.0 (標準化版)
--=============================================

-- 引入工具函數
dofile("./data/ablua/func/utils.lua")
dofile("./data/ablua/func/vars.lua")

-- 全域配置表
local dungeon_config = {}
local dungeon_state = {
    npc_index = -1  -- NPC 索引
}

-- 配置函數，由 main() 調用
function data()
    -- ===== 副本基本設定 =====
    dungeon_config.DUNGEON_NAME = "深淵之塔"
    dungeon_config.REQUIRED_PARTY_SIZE = 5          -- 必須五人組隊
    dungeon_config.COOLDOWN_HOURS = 24              -- CD時間24小時
    dungeon_config.MIN_LEVEL = 140                  -- 最低等級要求
    dungeon_config.MIN_REBIRTH = 3                  -- 最低轉生要求
    dungeon_config.ENTRANCE_MAP = 1000             -- 入口地圖
    dungeon_config.ENTRANCE_X = 100                -- 入口座標X
    dungeon_config.ENTRANCE_Y = 100                -- 入口座標Y
    dungeon_config.BOSS_ROUNDS = 5                 -- 副本共5關
    
    -- ===== 副本地圖配置 =====
    dungeon_config.DUNGEON_FLOORS = {
        {map = 50001, x = 15, y = 15, name = "第一層 - 守護者之間"},
        {map = 50002, x = 15, y = 15, name = "第二層 - 烈焰殿堂"},
        {map = 50003, x = 15, y = 15, name = "第三層 - 寒冰洞窟"},
        {map = 50004, x = 15, y = 15, name = "第四層 - 雷霆聖殿"},
        {map = 50005, x = 15, y = 15, name = "第五層 - 深淵王座"}
    }
    
    -- ===== BOSS配置 (敵人ID列表) =====
    dungeon_config.BOSS_CONFIG = {
        {2908, 2995, 2994, 2993, 2992},  -- 第一層
        {3001, 3002, 3003, 3004, 3005},  -- 第二層
        {3010, 3011, 3012, 3013, 3014},  -- 第三層
        {3020, 3021, 3022, 3023, 3024},  -- 第四層
        {3030}                            -- 第五層 - 最終BOSS
    }
    
    -- ===== 獎勵配置 =====
    dungeon_config.REWARDS = {
        -- 通關獎勵
        completion = {
            {itemId = 31180, amount = 3},  -- 高級強化石
            {itemId = 31181, amount = 3},  -- 高級防具石
            {itemId = 30626, amount = 1},  -- 特殊獎勵
            {exp = 50000},                 -- 經驗值
            {gold = 100000}                -- 金幣
        },
        -- 首次通關額外獎勵
        first_clear = {
            {itemId = 32044, amount = 1},  -- 稀有裝備
            {itemId = 31149, amount = 2}   -- 特殊材料
        }
    }
    
    -- 初始化隨機種子
    math.randomseed(tostring(os.time()):reverse():sub(1, 7))
end

-- ===== 核心功能函數 =====

-- 檢查隊伍是否符合進入條件
function checkPartyRequirements(leaderIndex)
    -- 檢查是否為隊長
    if char.getWorkInt(leaderIndex, "隊長") ~= 1 then
        char.TalkToCli(leaderIndex, -1, "[系統]: 只有隊長可以申請進入副本", "紅色")
        return false
    end
    
    -- 收集所有隊員
    local partyMembers = {leaderIndex}
    for i = 1, 5 do
        local memberIndex = char.getWorkInt(leaderIndex, "隊員" .. i)
        if char.check(memberIndex) == 1 then
            table.insert(partyMembers, memberIndex)
        end
    end
    
    -- 檢查隊伍人數
    if #partyMembers ~= dungeon_config.REQUIRED_PARTY_SIZE then
        char.TalkToCli(leaderIndex, -1, "[系統]: 需要正好" .. dungeon_config.REQUIRED_PARTY_SIZE .. "人組隊才能進入副本，目前隊伍人數: " .. #partyMembers, "紅色")
        return false
    end
    
    -- 檢查每個隊員的條件
    for _, memberIndex in ipairs(partyMembers) do
        -- 檢查等級  
        if char.getInt(memberIndex, "等級") < dungeon_config.MIN_LEVEL then
            char.TalkToCli(leaderIndex, -1, "[系統]: 隊員 " .. char.getChar(memberIndex, "姓名") .. " 等級不足" .. dungeon_config.MIN_LEVEL .. "級", "紅色")
            return false
        end
        
        -- 檢查轉生
        if char.getInt(memberIndex, "轉生") < dungeon_config.MIN_REBIRTH then
            char.TalkToCli(leaderIndex, -1, "[系統]: 隊員 " .. char.getChar(memberIndex, "姓名") .. " 轉生不足" .. dungeon_config.MIN_REBIRTH .. "轉", "紅色")
            return false
        end
        
        -- 檢查CD時間 (使用NPC臨時1來存放上次挑戰時間)
        local lastClearTime = char.getWorkInt(memberIndex, "NPC臨時1")
        local currentTime = os.time()
        local cdTime = dungeon_config.COOLDOWN_HOURS * 3600
        
        if lastClearTime > 0 and (currentTime - lastClearTime) < cdTime then
            local remainingTime = cdTime - (currentTime - lastClearTime)
            local hours = math.floor(remainingTime / 3600)
            local minutes = math.floor((remainingTime % 3600) / 60)
            char.TalkToCli(leaderIndex, -1, "[系統]: 隊員 " .. char.getChar(memberIndex, "姓名") .. " 仍在冷卻時間中，剩餘: " .. hours .. "小時" .. minutes .. "分鐘", "紅色")
            return false
        end
        
        -- 檢查背包空間
        if char.GetEmptyItemBoxNum(memberIndex) < 5 then
            char.TalkToCli(leaderIndex, -1, "[系統]: 隊員 " .. char.getChar(memberIndex, "姓名") .. " 背包空間不足", "紅色")
            return false
        end
    end
    
    return true, partyMembers
end

-- 將隊伍傳送到副本
function warpPartyToDungeon(partyMembers, floor)
    floor = floor or 1
    local floorConfig = dungeon_config.DUNGEON_FLOORS[floor]
    
    for _, memberIndex in ipairs(partyMembers) do
        char.WarpToSpecificPoint(memberIndex, floorConfig.map, floorConfig.x, floorConfig.y)
        -- 使用NPC臨時2存放當前樓層
        char.setWorkInt(memberIndex, "NPC臨時2", floor)
        -- 使用NPC臨時3標記正在副本中 (1=進行中)
        char.setWorkInt(memberIndex, "NPC臨時3", 1)
        
        char.TalkToCli(memberIndex, -1, "[系統]: 歡迎來到 " .. floorConfig.name, "綠色")
    end
end

-- 將隊伍傳送出副本
function warpPartyOutDungeon(partyMembers)
    for _, memberIndex in ipairs(partyMembers) do
        char.WarpToSpecificPoint(memberIndex, dungeon_config.ENTRANCE_MAP, dungeon_config.ENTRANCE_X, dungeon_config.ENTRANCE_Y)
        -- 清除副本狀態
        char.setWorkInt(memberIndex, "NPC臨時2", 0)  -- 清除樓層
        char.setWorkInt(memberIndex, "NPC臨時3", 0)  -- 清除副本狀態
    end
end

-- 給予獎勵
function giveRewards(partyMembers, rewardType)
    local rewardList = dungeon_config.REWARDS[rewardType]
    if not rewardList then return end
    
    for _, memberIndex in ipairs(partyMembers) do
        for _, reward in ipairs(rewardList) do
            if reward.itemId then
                for j = 1, reward.amount do
                    npc.AddItem(memberIndex, reward.itemId)
                end
                char.TalkToCli(memberIndex, -1, "[系統]: 獲得 " .. item.getNameFromNumber(reward.itemId) .. " x" .. reward.amount, "綠色")
            elseif reward.exp then
                npc.AddExps(memberIndex, tostring(reward.exp))
                char.TalkToCli(memberIndex, -1, "[系統]: 獲得經驗值 " .. reward.exp, "綠色")
            elseif reward.gold then
                npc.AddGold(memberIndex, reward.gold)
                char.TalkToCli(memberIndex, -1, "[系統]: 獲得金幣 " .. reward.gold, "綠色")
            end
        end
    end
end

-- 創建BOSS戰鬥
function createBossBattle(leaderIndex, floor)
    local bossConfig = dungeon_config.BOSS_CONFIG[floor]
    if not bossConfig then return false end
    
    local battleindex = battle.CreateVsEnemy(leaderIndex, dungeon_state.npc_index, bossConfig)
    if battleindex > -1 then
        battle.setLUAFunctionPointer(battleindex, "戰鬥函數", "DungeonBattleFinish", "")
        return true
    end
    return false
end

-- 戰鬥結束處理
function DungeonBattleFinish(meindex, battleindex, iswin)
    -- 獲取第一個參與戰鬥的角色
    local charaindex = battle.getCharOne(battleindex, 0, 0)
    if char.check(charaindex) == 0 then return end
    
    local currentFloor = char.getWorkInt(charaindex, "NPC臨時2")
    
    -- 檢查是否在副本中
    if currentFloor == 0 then return end
    
    -- 收集隊伍成員
    local partyMembers = {charaindex}
    if char.getWorkInt(charaindex, "隊長") == 1 then
        for i = 1, 5 do
            local memberIndex = char.getWorkInt(charaindex, "隊員" .. i)
            if char.check(memberIndex) == 1 then
                table.insert(partyMembers, memberIndex)
            end
        end
    end
    
    -- 檢查是否獲勝
    if iswin == 1 then
        -- 勝利處理
        if currentFloor < #dungeon_config.DUNGEON_FLOORS then
            -- 不是最後一層，進入下一層
            currentFloor = currentFloor + 1
            warpPartyToDungeon(partyMembers, currentFloor)
            
            for _, memberIndex in ipairs(partyMembers) do
                char.TalkToCli(memberIndex, -1, "[系統]: 恭喜通過第" .. (currentFloor-1) .. "層！準備挑戰第" .. currentFloor .. "層", "黃色")
            end
        else
            -- 通關處理
            for _, memberIndex in ipairs(partyMembers) do
                -- 設定CD時間 (使用NPC臨時1)
                char.setWorkInt(memberIndex, "NPC臨時1", os.time())
                
                -- 檢查是否首次通關 (使用NPC臨時4，0=未通關，1=已通關)
                local hasCleared = char.getWorkInt(memberIndex, "NPC臨時4")
                if hasCleared == 0 then
                    char.setWorkInt(memberIndex, "NPC臨時4", 1)
                    giveRewards(partyMembers, "first_clear")
                    char.TalkToCli(memberIndex, -1, "[系統]: 首次通關獎勵！", "黃色")
                end
            end
            
            -- 給予通關獎勵
            giveRewards(partyMembers, "completion")
            
            -- 全服公告
            char.talkToServer(-1, "[系統]: 恭喜隊伍 [" .. char.getChar(charaindex, "姓名") .. "] 成功通關副本《" .. dungeon_config.DUNGEON_NAME .. "》！", 20)
            
            -- 傳送出副本
            warpPartyOutDungeon(partyMembers)
        end
    else
        -- 失敗處理
        for _, memberIndex in ipairs(partyMembers) do
            char.TalkToCli(memberIndex, -1, "[系統]: 挑戰失敗，5秒後將被傳送出副本", "紅色")
        end
        
        -- 延遲傳送 (使用NPC臨時5存放傳送時間，臨時6存放隊長索引)
        char.setWorkInt(dungeon_state.npc_index, "NPC臨時5", os.time() + 5)
        char.setWorkInt(dungeon_state.npc_index, "NPC臨時6", charaindex)
    end
end

-- 檢查傳送計時器
function checkWarpTimer()
    local warpTime = char.getWorkInt(dungeon_state.npc_index, "NPC臨時5")
    if warpTime > 0 and os.time() >= warpTime then
        local leaderIndex = char.getWorkInt(dungeon_state.npc_index, "NPC臨時6")
        if char.check(leaderIndex) == 1 then
            local partyMembers = {leaderIndex}
            if char.getWorkInt(leaderIndex, "隊長") == 1 then
                for i = 1, 5 do
                    local memberIndex = char.getWorkInt(leaderIndex, "隊員" .. i)
                    if char.check(memberIndex) == 1 then
                        table.insert(partyMembers, memberIndex)
                    end
                end
            end
            warpPartyOutDungeon(partyMembers)
        end
        
        -- 清除計時器
        char.setWorkInt(dungeon_state.npc_index, "NPC臨時5", 0)
        char.setWorkInt(dungeon_state.npc_index, "NPC臨時6", -1)
    end
end

-- ===== NPC 對話函數 =====

function Talked(meindex, talkerindex, szMes, color)
    if npc.isFaceToFace(meindex, talkerindex) ~= 1 then return end
    
    -- 檢查CD時間 (使用NPC臨時1)
    local lastClearTime = char.getWorkInt(talkerindex, "NPC臨時1")
    local currentTime = os.time()
    local cdTime = dungeon_config.COOLDOWN_HOURS * 3600
    local remainingCD = 0
    
    if lastClearTime > 0 then
        remainingCD = cdTime - (currentTime - lastClearTime)
        if remainingCD < 0 then remainingCD = 0 end
    end
    
    local token = "4                    《" .. dungeon_config.DUNGEON_NAME .. "》\\n\\n"
    token = token .. "這是一個需要" .. dungeon_config.REQUIRED_PARTY_SIZE .. "人組隊挑戰的高難度副本\\n"
    token = token .. "進入條件：等級" .. dungeon_config.MIN_LEVEL .. "以上，" .. dungeon_config.MIN_REBIRTH .. "轉以上\\n"
    token = token .. "冷卻時間：" .. dungeon_config.COOLDOWN_HOURS .. "小時\\n\\n"
    
    if remainingCD > 0 then
        local hours = math.floor(remainingCD / 3600)
        local minutes = math.floor((remainingCD % 3600) / 60)
        token = token .. "你的冷卻時間：" .. hours .. "小時" .. minutes .. "分鐘\\n\\n"
    else
        token = token .. "你可以進入副本\\n\\n"
    end
    
    token = token .. "                        我要挑戰副本\\n"
    token = token .. "                        查看副本說明\\n"
    token = token .. "                        查看獎勵列表"
    
    lssproto.windows(talkerindex, "對話窗", "確定", 1, char.getWorkInt(meindex, "對象"), token)
end

function WindowTalked(meindex, talkerindex, seqno, select, data)
    if npc.isFaceToFace(meindex, talkerindex) ~= 1 then return end
    
    if select == 0 then
        local choice = other.atoi(data)
        
        if seqno == 1 then
            if choice == 1 then
                -- 挑戰副本
                local success, partyMembers = checkPartyRequirements(talkerindex)
                if success then
                    local token = "確定要進入副本嗎？\\n\\n"
                    token = token .. "隊伍成員：\\n"
                    for _, memberIndex in ipairs(partyMembers) do
                        token = token .. "- " .. char.getChar(memberIndex, "姓名") .. " (Lv." .. char.getInt(memberIndex, "等級") .. ")\\n"
                    end
                    token = token .. "\\n注意：進入後無法中途退出！"
                    
                    lssproto.windows(talkerindex, "對話窗", "確定進入|取消", 2, char.getWorkInt(meindex, "對象"), token)
                end
                
            elseif choice == 2 then
                -- 查看說明
                local token = "《" .. dungeon_config.DUNGEON_NAME .. "》副本說明\\n\\n"
                token = token .. "• 需要" .. dungeon_config.REQUIRED_PARTY_SIZE .. "人組隊挑戰\\n"
                token = token .. "• 共有" .. #dungeon_config.DUNGEON_FLOORS .. "層，每層都有強大的BOSS\\n"
                token = token .. "• 必須按順序通過每一層\\n"
                token = token .. "• 失敗將被傳送出副本\\n"
                token = token .. "• 每" .. dungeon_config.COOLDOWN_HOURS .. "小時只能挑戰一次\\n"
                token = token .. "• 首次通關有額外獎勵"
                
                lssproto.windows(talkerindex, "對話窗", "確定", -1, char.getWorkInt(meindex, "對象"), token)
                
            elseif choice == 3 then
                -- 查看獎勵
                local token = "《" .. dungeon_config.DUNGEON_NAME .. "》獎勵列表\\n\\n"
                token = token .. "通關獎勵：\\n"
                for _, reward in ipairs(dungeon_config.REWARDS.completion) do
                    if reward.itemId then
                        token = token .. "• " .. item.getNameFromNumber(reward.itemId) .. " x" .. reward.amount .. "\\n"
                    elseif reward.exp then
                        token = token .. "• 經驗值 " .. reward.exp .. "\\n"
                    elseif reward.gold then
                        token = token .. "• 金幣 " .. reward.gold .. "\\n"
                    end
                end
                
                token = token .. "\\n首次通關額外獎勵：\\n"
                for _, reward in ipairs(dungeon_config.REWARDS.first_clear) do
                    if reward.itemId then
                        token = token .. "• " .. item.getNameFromNumber(reward.itemId) .. " x" .. reward.amount .. "\\n"
                    end
                end
                
                lssproto.windows(talkerindex, "對話窗", "確定", -1, char.getWorkInt(meindex, "對象"), token)
            end
            
        elseif seqno == 2 then
            if choice == 1 then
                -- 確定進入副本
                local success, partyMembers = checkPartyRequirements(talkerindex)
                if success then
                    warpPartyToDungeon(partyMembers, 1)
                    char.TalkToCli(talkerindex, -1, "[系統]: 副本挑戰開始！祝你們好運！", "綠色")
                end
            end
        end
    end
end

-- ===== 循環檢測函數 =====

function Loop(meindex)
    -- 檢查傳送計時器
    checkWarpTimer()
    
    -- 檢查副本中的玩家狀態
    local maxplayer = char.getPlayerMaxNum()
    for i = 0, maxplayer - 1 do
        if char.check(i) == 1 then
            local currentFloor = char.getWorkInt(i, "NPC臨時2")  -- 使用NPC臨時2存放樓層
            if currentFloor > 0 then
                -- 在副本中的玩家
                local playerMap = char.getInt(i, "地圖號")
                local expectedMap = dungeon_config.DUNGEON_FLOORS[currentFloor].map
                
                -- 檢查是否在正確的地圖
                if playerMap ~= expectedMap then
                    -- 可能斷線重連，傳送回正確位置
                    local floorConfig = dungeon_config.DUNGEON_FLOORS[currentFloor]
                    char.WarpToSpecificPoint(i, floorConfig.map, floorConfig.x, floorConfig.y)
                end
            end
        end
    end
end

-- ===== 初始化函數 =====

function Create(name, metamo, floor, x, y, dir)
    local index = npc.CreateNpc(name, metamo, floor, x, y, dir)
    char.setFunctionPointer(index, "對話函數", "Talked", "")
    char.setFunctionPointer(index, "視窗函數", "WindowTalked", "")
    char.setFunctionPointer(index, "循環函數", "Loop", "")
    
    -- 設定循環檢測間隔（每5秒檢測一次）
    char.setInt(index, "循環函數時間", 5000)
    
    return index
end

-- 主函數，由遊戲引擎載入腳本時調用
function main()
    data()  -- 載入所有配置
    dungeon_state.npc_index = Create(dungeon_config.DUNGEON_NAME .. "守門人", 101813, dungeon_config.ENTRANCE_MAP, dungeon_config.ENTRANCE_X, dungeon_config.ENTRANCE_Y, 6)
    print("五人組隊副本系統已載入：" .. dungeon_config.DUNGEON_NAME)
end