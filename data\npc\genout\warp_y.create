NPCCREATE
# generated by npcgen.perl which is made by ringo
#
#
# infile : data/npc/sainasu/warp_y.gen
#line 23
{
floorid=1000
borncorner=63,99,63,99
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1001|10|15
}
# between
{
floorid=1001
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|63|99
}
#line 24
{
floorid=1000
borncorner=63,100,63,100
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1001|10|16
}
# between
{
floorid=1001
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|63|100
}
#line 25
{
floorid=1000
borncorner=63,98,63,98
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1001|10|15
}
#line 27
{
floorid=1000
borncorner=67,108,67,108
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1002|10|15
}
# between
{
floorid=1002
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|67|108
}
#line 28
{
floorid=1000
borncorner=67,109,67,109
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1002|10|16
}
# between
{
floorid=1002
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|67|109
}
#line 29
{
floorid=1000
borncorner=67,107,67,107
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1002|10|15
}
#line 31
{
floorid=1000
borncorner=69,72,69,72
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1003|16|21
}
# between
{
floorid=1003
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|69|72
}
#line 32
{
floorid=1000
borncorner=68,72,68,72
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1003|15|21
}
# between
{
floorid=1003
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|68|72
}
#line 34
{
floorid=1000
borncorner=54,81,54,81
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1004|16|21
}
# between
{
floorid=1004
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|54|81
}
#line 35
{
floorid=1000
borncorner=53,81,53,81
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1004|15|21
}
# between
{
floorid=1004
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|53|81
}
#line 37
{
floorid=1000
borncorner=81,66,81,66
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1005|16|21
}
# between
{
floorid=1005
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|81|66
}
#line 38
{
floorid=1000
borncorner=80,66,80,66
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1005|15|21
}
# between
{
floorid=1005
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|80|66
}
#line 40
{
floorid=1000
borncorner=98,44,98,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1006|10|20
}
# between
{
floorid=1006
borncorner=10,20,10,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|98|44
}
#line 41
{
floorid=1000
borncorner=98,45,98,45
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1006|10|21
}
# between
{
floorid=1006
borncorner=10,21,10,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|98|45
}
#line 43
{
floorid=1000
borncorner=106,91,106,91
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1009|10|25
}
# between
{
floorid=1009
borncorner=10,25,10,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|106|91
}
#line 44
{
floorid=1000
borncorner=106,92,106,92
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1009|10|26
}
# between
{
floorid=1009
borncorner=10,26,10,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|106|92
}
#line 51
{
floorid=1007
borncorner=48,62,48,62
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|55|60
}
#line 52
{
floorid=1007
borncorner=49,62,49,62
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|56|60
}
#line 56
{
floorid=1000
borncorner=97,110,97,110
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1020|4|12
}
# between
{
floorid=1020
borncorner=4,12,4,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|97|110
}
#line 57
{
floorid=1000
borncorner=97,111,97,111
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1020|4|12
}
#line 59
{
floorid=1008
borncorner=12,20,12,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1020|12|9
}
#line 60
{
floorid=1008
borncorner=12,21,12,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1020|12|9
}
#line 62
{
floorid=1022
borncorner=6,14,6,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1020|12|9
}
#line 63
{
floorid=1022
borncorner=7,14,7,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1020|12|9
}
#line 64
{
floorid=1022
borncorner=8,14,8,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1020|12|9
}
#line 68
{
floorid=1000
borncorner=63,117,63,117
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1011|10|15|M
}
# between
{
floorid=1011
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|63|117
}
#line 69
{
floorid=1011
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|63|117
}
#line 71
{
floorid=1000
borncorner=63,118,63,118
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1014|10|16|A
}
# between
{
floorid=1014
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|63|118
}
#line 72
{
floorid=1014
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|63|118
}
#line 76
{
floorid=1000
borncorner=112,37,112,37
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1012|15|21
}
# between
{
floorid=1012
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|112|37
}
#line 77
{
floorid=1000
borncorner=113,37,113,37
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1012|16|21
}
# between
{
floorid=1012
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1000|113|37
}
#line 88
{
floorid=1100
borncorner=52,72,52,72
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1101|15|21
}
# between
{
floorid=1101
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1100|52|72
}
#line 89
{
floorid=1101
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1100|52|72
}
#line 91
{
floorid=1100
borncorner=69,77,69,77
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1102|16|21
}
# between
{
floorid=1102
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1100|69|77
}
#line 92
{
floorid=1100
borncorner=68,77,68,77
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1102|15|21
}
# between
{
floorid=1102
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1100|68|77
}
#line 94
{
floorid=1100
borncorner=61,109,61,109
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1103|15|21
}
# between
{
floorid=1103
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1100|61|109
}
#line 95
{
floorid=1100
borncorner=62,109,62,109
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1103|16|21
}
# between
{
floorid=1103
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1100|62|109
}
#line 97
{
floorid=1100
borncorner=68,97,68,97
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1104|15|21
}
# between
{
floorid=1104
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1100|68|97
}
#line 98
{
floorid=1100
borncorner=69,97,69,97
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1104|16|21
}
# between
{
floorid=1104
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1100|69|97
}
#line 100
{
floorid=1100
borncorner=88,80,88,80
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1105|10|16
}
# between
{
floorid=1105
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1100|88|80
}
#line 101
{
floorid=1100
borncorner=88,79,88,79
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1105|10|15
}
# between
{
floorid=1105
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1100|88|79
}
#line 103
{
floorid=1100
borncorner=55,93,55,93
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1106|21|21
}
# between
{
floorid=1106
borncorner=21,21,21,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1100|55|93
}
#line 104
{
floorid=1100
borncorner=54,93,54,93
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1106|20|21
}
# between
{
floorid=1106
borncorner=20,21,20,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1100|54|93
}
#line 112
{
floorid=1100
borncorner=82,112,82,112
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1112|10|15
}
# between
{
floorid=1112
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1100|82|112
}
#line 113
{
floorid=1100
borncorner=82,113,82,113
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1112|10|16
}
# between
{
floorid=1112
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1100|82|113
}
#line 122
{
floorid=1300
borncorner=43,43,43,43
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1301|15|21
}
# between
{
floorid=1301
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1300|43|43
}
#line 123
{
floorid=1300
borncorner=44,43,44,43
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1301|16|21
}
# between
{
floorid=1301
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1300|44|43
}
#line 125
{
floorid=1300
borncorner=34,42,34,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1302|16|21
}
# between
{
floorid=1302
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1300|34|42
}
#line 126
{
floorid=1300
borncorner=33,42,33,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1302|15|21
}
# between
{
floorid=1302
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1300|33|42
}
#line 128
{
floorid=1300
borncorner=23,43,23,43
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1303|15|21
}
# between
{
floorid=1303
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1300|23|43
}
#line 129
{
floorid=1300
borncorner=24,43,24,43
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1303|16|21
}
# between
{
floorid=1303
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1300|24|43
}
#line 131
{
floorid=1300
borncorner=66,42,66,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1304|15|21
}
# between
{
floorid=1304
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1300|66|42
}
#line 132
{
floorid=1300
borncorner=67,42,67,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1304|16|21
}
# between
{
floorid=1304
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1300|67|42
}
#line 134
{
floorid=1300
borncorner=54,50,54,50
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1305|10|16
}
# between
{
floorid=1305
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1300|54|50
}
#line 135
{
floorid=1300
borncorner=54,49,54,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1305|10|15
}
# between
{
floorid=1305
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1300|54|49
}
#line 137
{
floorid=1300
borncorner=53,26,53,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1306|21|21
}
# between
{
floorid=1306
borncorner=21,21,21,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1300|53|26
}
#line 138
{
floorid=1300
borncorner=52,26,52,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1306|20|21
}
# between
{
floorid=1306
borncorner=20,21,20,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1300|52|26
}
#line 139
{
floorid=1300
borncorner=54,26,54,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1306|21|21
}
# between
{
floorid=1306
borncorner=21,21,21,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1300|54|26
}
#line 144
{
floorid=1300
borncorner=68,44,68,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1310|10|15
}
# between
{
floorid=1310
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1300|68|44
}
#line 145
{
floorid=1300
borncorner=68,45,68,45
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1310|10|16
}
# between
{
floorid=1310
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1300|68|45
}
#line 162
{
floorid=1400
borncorner=81,83,81,83
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1401|15|21
}
# between
{
floorid=1401
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1400|81|83
}
#line 163
{
floorid=1400
borncorner=82,83,82,83
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1401|16|21
}
# between
{
floorid=1401
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1400|82|83
}
#line 165
{
floorid=1400
borncorner=90,90,90,90
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1402|10|15
}
# between
{
floorid=1402
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1400|90|90
}
#line 166
{
floorid=1400
borncorner=90,91,90,91
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1402|10|16
}
# between
{
floorid=1402
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1400|90|91
}
#line 168
{
floorid=1400
borncorner=84,97,84,97
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1403|10|15
}
# between
{
floorid=1403
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1400|84|97
}
#line 169
{
floorid=1400
borncorner=84,98,84,98
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1403|10|16
}
# between
{
floorid=1403
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1400|84|98
}
#line 171
{
floorid=1400
borncorner=74,89,74,89
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1404|15|21
}
# between
{
floorid=1404
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1400|74|89
}
#line 172
{
floorid=1400
borncorner=75,89,75,89
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1404|16|21
}
# between
{
floorid=1404
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1400|75|89
}
#line 174
{
floorid=1400
borncorner=89,76,89,76
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1405|15|21
}
# between
{
floorid=1405
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1400|89|76
}
#line 175
{
floorid=1400
borncorner=90,76,90,76
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1405|16|21
}
# between
{
floorid=1405
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1400|90|76
}
#line 177
{
floorid=1400
borncorner=87,85,87,85
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1406|11|25
}
# between
{
floorid=1406
borncorner=11,25,11,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1400|87|85
}
#line 178
{
floorid=1400
borncorner=88,85,88,85
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1406|12|25
}
# between
{
floorid=1406
borncorner=12,25,12,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1400|88|85
}
#line 179
{
floorid=1400
borncorner=87,84,87,84
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1406|11|24
}
# between
{
floorid=1406
borncorner=11,24,11,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1400|87|84
}
#line 193
{
floorid=2000
borncorner=92,89,92,89
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2001|10|15
}
# between
{
floorid=2001
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2000|92|89
}
#line 194
{
floorid=2000
borncorner=92,90,92,90
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2001|10|16
}
# between
{
floorid=2001
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2000|92|90
}
#line 196
{
floorid=2000
borncorner=73,73,73,73
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2002|15|21
}
# between
{
floorid=2002
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2000|73|73
}
#line 197
{
floorid=2000
borncorner=74,73,74,73
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2002|16|21
}
# between
{
floorid=2002
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2000|74|73
}
#line 199
{
floorid=2000
borncorner=75,57,75,57
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2003|10|15
}
# between
{
floorid=2003
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2000|75|57
}
#line 200
{
floorid=2000
borncorner=75,58,75,58
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2003|10|16
}
# between
{
floorid=2003
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2000|75|58
}
#line 202
{
floorid=2000
borncorner=92,77,92,77
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2004|15|21
}
# between
{
floorid=2004
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2000|92|77
}
#line 203
{
floorid=2000
borncorner=93,77,93,77
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2004|16|21
}
# between
{
floorid=2004
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2000|93|77
}
#line 205
{
floorid=2005
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2000|66|82
}
#line 206
{
floorid=2000
borncorner=66,82,66,82
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2005|10|16
}
# between
{
floorid=2005
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2000|66|82
}
#line 208
{
floorid=2000
borncorner=56,48,56,48
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2006|20|21
}
# between
{
floorid=2006
borncorner=20,21,20,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2000|56|48
}
#line 209
{
floorid=2000
borncorner=57,48,57,48
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2006|21|21
}
# between
{
floorid=2006
borncorner=21,21,21,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2000|57|48
}
#line 210
{
floorid=2000
borncorner=58,48,58,48
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2006|21|21
}
#line 215
{
floorid=2000
borncorner=73,103,73,103
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2009|10|25
}
# between
{
floorid=2009
borncorner=10,25,10,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2000|73|103
}
#line 216
{
floorid=2000
borncorner=73,104,73,104
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2009|10|26
}
# between
{
floorid=2009
borncorner=10,26,10,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2000|73|104
}
#line 220
{
floorid=2007
borncorner=48,76,48,76
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2000|84|78
}
#line 224
{
floorid=2000
borncorner=103,80,103,80
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2011|15|21|M
}
# between
{
floorid=2011
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2000|103|80
}
#line 225
{
floorid=2011
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2000|103|80
}
#line 227
{
floorid=2000
borncorner=102,80,102,80
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2014|16|21|A
}
# between
{
floorid=2014
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2000|102|80
}
#line 228
{
floorid=2014
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2000|102|80
}
#line 233
{
floorid=2000
borncorner=50,71,50,71
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2012|15|21
}
# between
{
floorid=2012
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2000|50|71
}
#line 234
{
floorid=2000
borncorner=51,71,51,71
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2012|16|21
}
# between
{
floorid=2012
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|2000|51|71
}
#line 240
{
floorid=1200
borncorner=51,72,51,72
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1201|15|21
}
# between
{
floorid=1201
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1200|51|72
}
#line 241
{
floorid=1200
borncorner=52,72,52,72
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1201|16|21
}
# between
{
floorid=1201
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1200|52|72
}
#line 243
{
floorid=1200
borncorner=68,77,68,77
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1202|15|21
}
# between
{
floorid=1202
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1200|68|77
}
#line 244
{
floorid=1200
borncorner=69,77,69,77
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1202|16|21
}
# between
{
floorid=1202
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1200|69|77
}
#line 246
{
floorid=1200
borncorner=39,34,39,34
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1203|15|21
}
# between
{
floorid=1203
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1200|39|34
}
#line 247
{
floorid=1200
borncorner=40,34,40,34
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1203|16|21
}
# between
{
floorid=1203
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1200|40|34
}
#line 249
{
floorid=1200
borncorner=33,39,33,39
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1204|15|21
}
# between
{
floorid=1204
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1200|33|39
}
#line 250
{
floorid=1200
borncorner=34,39,34,39
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1204|16|21
}
# between
{
floorid=1204
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1200|34|39
}
#line 252
{
floorid=1200
borncorner=61,50,61,50
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1205|10|15
}
# between
{
floorid=1205
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1200|61|50
}
#line 253
{
floorid=1200
borncorner=61,51,61,51
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1205|10|16
}
# between
{
floorid=1205
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1200|61|51
}
#line 255
{
floorid=1200
borncorner=54,92,54,92
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1206|20|21
}
# between
{
floorid=1206
borncorner=20,21,20,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1200|54|92
}
#line 256
{
floorid=1200
borncorner=55,92,55,92
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1206|21|21
}
# between
{
floorid=1206
borncorner=21,21,21,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1200|55|92
}
#line 257
{
floorid=1200
borncorner=56,92,56,92
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1206|21|21
}
#line 259
{
floorid=1200
borncorner=82,112,82,112
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1210|10|15
}
# between
{
floorid=1210
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1200|82|112
}
#line 260
{
floorid=1200
borncorner=82,113,82,113
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1210|10|16
}
# between
{
floorid=1210
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1200|82|113
}
#line 270
{
floorid=1200
borncorner=87,103,87,103
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1212|10|15
}
# between
{
floorid=1212
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1200|87|103
}
#line 271
{
floorid=1200
borncorner=87,104,87,104
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1212|10|16
}
# between
{
floorid=1212
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1200|87|104
}
#line 282
{
floorid=100
borncorner=184,339,184,339
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|101|15|21
}
# between
{
floorid=101
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|100|184|339
}
#line 283
{
floorid=100
borncorner=185,339,185,339
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|101|16|21
}
# between
{
floorid=101
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|100|185|339
}
