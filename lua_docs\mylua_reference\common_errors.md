# mylua 常見錯誤避免指南

## 🚨 關鍵錯誤類型

### 1. 變數名稱錯誤
```lua
-- ❌ 常見錯誤
char.getInt(talkerindex, "家族編號")  -- 此變數不存在

-- ✅ 正確方式 (已檢查 mylua/charbase.c)
char.getInt(talkerindex, "家族索引")  -- CHAR_FMINDEX

-- 🔍 檢查方法
grep -r "家族" mylua/charbase.c
```

### 2. 函數參數錯誤
```lua
-- ❌ 常見錯誤
other.callfunction("./file.lua", "func", param)  -- 錯誤的函數名和參數順序

-- ✅ 正確方式 (已檢查 mylua/otherbase.c)
other.CallFunction("func", "./file.lua", {param})

-- 🔍 檢查方法
grep -r "CallFunction" mylua/otherbase.c
```

### 3. 編碼問題
```lua
-- ❌ 常見錯誤
-- 使用 UTF-8 編碼導致中文亂碼

-- ✅ 正確方式
-- 使用 Big5 編碼，參考現有系統腳本如 pool.lua
```

### 4. NPC 創建遺漏
```lua
-- ❌ 常見錯誤
-- 腳本中有 WindowTalked 函數但沒有創建 NPC

-- ✅ 正確方式
function main()
    data()
    -- 在 main() 函數中創建 NPC 並設置函數指針
    local npcindex = npc.CreateNpc(name, metamo, floor, x, y, dir)
    char.setFunctionPointer(npcindex, "對話函數", "Talked", "")
    char.setFunctionPointer(npcindex, "視窗函數", "WindowTalked", "")
end
```

### 5. 窗口函數參數錯誤
```lua
-- ❌ 常見錯誤
lssproto.windows(talkerindex, 1103, 8, 0, 999999, data)  -- 錯誤的對象索引

-- ✅ 正確方式 (已檢查 mylua/lssprotobase.c，參考 pool.lua)
lssproto.windows(talkerindex, 1103, 8, 0, char.getWorkInt(npcindex, "對象"), data)

-- 🔍 檢查方法
grep -r "windows_send" mylua/lssprotobase.c
```

## 📋 錯誤診斷檢查清單

### 當 NPC 沒有出現時
1. ✅ 檢查 main() 函數是否正確調用
2. ✅ 檢查 npc.CreateNpc 的返回值
3. ✅ 檢查 NPC 座標是否正確
4. ✅ 檢查地圖編號是否存在
5. ✅ 查看服務器日誌確認 NPC 創建狀態

### 當對話沒有反應時
1. ✅ 檢查函數指針是否正確設置
2. ✅ 確認 Talked 函數存在且拼寫正確
3. ✅ 檢查 npc.isFaceToFace 條件
4. ✅ 檢查 Big5 編碼是否正確

### 當窗口無法操作時
1. ✅ 確認 WindowTalked 函數存在
2. ✅ 檢查窗口函數參數是否正確
3. ✅ 驗證對象索引是否正確
4. ✅ 檢查 other.CallFunction 調用是否正確

### 當家族變數獲取失敗時
1. ✅ 確認使用 "家族索引" 而不是 "家族編號"
2. ✅ 檢查 mylua/charbase.c 中的變數名稱
3. ✅ 驗證玩家是否已加入家族
4. ✅ 測試變數是否返回預期值

### 當道具操作失敗時
1. ✅ 使用 char.countitem 而不是手動遍歷
2. ✅ 使用 npc.DelItemNum 而不是手動刪除
3. ✅ 檢查道具ID是否正確
4. ✅ 確認參數格式 "itemid,count"

## 🔧 錯誤修正範例

### 範例 1: 修正家族變數錯誤
```lua
-- ❌ 錯誤代碼
local family_number = char.getInt(talkerindex, "家族編號")
if family_number > 0 then
    -- 邏輯處理
end

-- ✅ 修正後代碼 (已檢查 mylua/charbase.c)
local family_index = char.getInt(talkerindex, "家族索引")  -- CHAR_FMINDEX
local family_status = char.getInt(talkerindex, "家族地位")  -- CHAR_FMLEADERFLAG
if family_status >= 1 then  -- FMMEMBER_MEMBER 或以上
    -- 邏輯處理
end
```

### 範例 2: 修正跨文件調用錯誤
```lua
-- ❌ 錯誤代碼
dofile("./familypool.lua")
openFamilyItemStorage(talkerindex)

-- ✅ 修正後代碼 (已檢查 mylua/otherbase.c)
other.CallFunction("openFamilyItemStorage", "./familypool.lua", {talkerindex, meindex})
```

### 範例 3: 修正道具操作錯誤
```lua
-- ❌ 錯誤代碼 (手動遍歷)
local count = 0
for i = 9, 23 do
    local itemindex = char.getItemIndex(talkerindex, i)
    if itemindex >= 0 and item.getInt(itemindex, "序號") == itemid then
        count = count + item.getInt(itemindex, "堆疊")
    end
end

-- ✅ 修正後代碼 (已檢查 mylua/charbase.c)
local count = char.countitem(talkerindex, itemid)
```

### 範例 4: 修正窗口函數錯誤
```lua
-- ❌ 錯誤代碼
lssproto.windows(talkerindex, 1103, 8, 0, 999999, genItemDataList(talkerindex))

-- ✅ 修正後代碼 (已檢查 mylua/lssprotobase.c，參考 pool.lua)
lssproto.windows(talkerindex, 1103, 8, 0, char.getWorkInt(npcindex, "對象"), genItemDataList(talkerindex))
```

## 🛠️ 預防措施

### 1. 建立檢查習慣
```bash
# 每次使用新函數前都要檢查
grep -r "function_name" mylua/
grep -r "變數名" mylua/charbase.c
```

### 2. 參考現有實現
```lua
-- 學習 pool.lua, donate.lua 等現有系統的實現方式
-- 不要重新發明輪子
```

### 3. 記錄檢查結果
```lua
-- ✅ 已檢查 mylua/lssprotobase.c 確認正確用法
lssproto.windows(talkerindex, 1103, 8, 0, char.getWorkInt(npcindex, "對象"), data)
```

### 4. 使用正確編碼
```lua
-- 確保文件使用 Big5 編碼
-- 參考現有系統腳本的編碼格式
```

## 📚 錯誤學習資源

### 源碼檢查位置
- `mylua/*.c` - C 函數實現
- `include/*.h` - 常數和枚舉定義
- `data/ablua/npc/system/*.lua` - 現有系統腳本範例

### 參考腳本
- `pool.lua` - 倉庫系統，學習 NPC 創建和窗口處理
- `donate.lua` - 捐獻系統，學習道具操作
- `fmreward.lua` - 家族獎勵，學習家族變數使用

### 常用檢查命令
```bash
# 檢查函數定義
grep -r "function_name" mylua/

# 檢查變數名稱
grep -r "變數名" mylua/charbase.c

# 檢查枚舉常數
grep -r "ENUM_NAME" include/

# 查看現有腳本用法
grep -r "function_name" data/ablua/npc/system/
```

## 🎯 錯誤避免總結

### 核心原則
1. **永遠檢查源碼**: 不要憑猜測使用函數
2. **參考現有實現**: 學習正確的使用模式
3. **記錄檢查結果**: 在代碼中註釋檢查過的文件
4. **使用正確編碼**: Big5 編碼確保中文顯示

### 常見錯誤模式
1. **變數名稱錯誤**: 使用不存在的變數名
2. **函數參數錯誤**: 參數順序或格式錯誤
3. **編碼問題**: UTF-8 導致中文亂碼
4. **NPC 創建遺漏**: 有事件處理但沒有 NPC
5. **窗口參數錯誤**: 使用錯誤的對象索引

### 預防策略
1. **建立檢查習慣**: 每次使用前都檢查源碼
2. **學習現有實現**: 參考成功的系統腳本
3. **記錄和分享**: 將發現記錄在文檔中
4. **測試驗證**: 充分測試所有功能

遵循這些原則可以大大減少開發中的錯誤，提高代碼質量和開發效率。

---

**版本**: 1.4  
**作者**: Augment Agent  
**最後更新**: 2025-06-18  
**說明**: mylua 開發的通用錯誤避免指南，適用於所有 Stone Age Lua 項目
