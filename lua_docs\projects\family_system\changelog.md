# 家族建設系統 - 更新日誌

## 版本 1.5 (2025-06-18) - 文檔結構重組版本

### 📋 文檔結構重大改進
- **重新組織文檔結構**: 採用方案 A - Lua 在外，項目在內
- **創建 lua_docs 根目錄**: 通用 Lua 開發文檔與具體項目分離
- **新的文檔結構**:
  ```
  lua_docs/
  ├── mylua_reference/           # 通用 mylua 參考文檔
  └── projects/family_system/    # 家族系統項目文檔
  ```

### ✅ 文檔分類優化
- **通用 mylua 文檔**: 適用於所有 Stone Age Lua 項目
- **項目特定文檔**: 專注於家族系統的實現細節
- **清晰的導航**: 每個層級都有完整的索引和說明

### 🎯 便於未來擴展
- 可以輕鬆添加其他項目 (PVP 系統、商店系統等)
- 通用的 mylua 知識可以被所有項目重用
- AI 助手可以快速找到對應類型的文檔

## 版本 1.4 (2025-06-18) - 窗口函數修正版本

### 🚨 關鍵問題修正
- **修正 lssproto.windows 參數**: 檢查了 mylua/lssprotobase.c，修正對象索引參數
- **更新函數調用**: 傳遞正確的 NPC 索引給倉庫函數

### ✅ 技術改進
- 使用正確的 `char.getWorkInt(npcindex, "對象")` 作為對象索引
- 參考 pool.lua 的正確實現模式
- 添加詳細的參數說明和檢查記錄

### 📋 文檔改進
- 更新編程參考添加窗口函數使用指南
- 更新部署指南說明正確的參數用法
- 強調源碼檢查的重要性

## 版本 1.3 (2025-06-18) - 重大修正版本

### 🚨 關鍵問題修正
- **修正 NPC 創建問題**: 添加了正確的 NPC 創建和函數指針設置
- **修正窗口事件處理**: 整合倉庫窗口事件到主系統中

### ✅ 技術改進
- 使用正確的 `npc.CreateNpc` 創建 NPC
- 設置正確的函數指針 ("對話函數", "視窗函數")
- 整合窗口事件處理到 family_system.lua
- familypool.lua 作為純函數庫，不創建 NPC

### 📋 文檔改進
- 創建統一的文檔資料夾
- 重新組織所有技術文檔
- 添加詳細的 NPC 創建指南
- 更新部署指南和故障排除

## 版本 1.2 (2025-06-18) - 函數和變數修正版本

### ✅ 函數用法修正
- **道具操作**: 使用 `char.countitem` 和 `npc.DelItemNum`
- **家族變數**: 使用 "家族索引" 而不是 "家族編號"
- **跨文件調用**: 使用正確的 `other.CallFunction`

### ✅ 邏輯簡化
- **移除隊伍檢查**: 建設功能只需要族長身份
- **移除工具函數引入**: 不引入未使用的 utils.lua
- **簡化申請條件**: 專注於核心要求

### ✅ 文件位置修正
- **移至正確目錄**: `data/ablua/npc/system/`
- **使用 Big5 編碼**: 確保中文顯示正常
- **參考現有模式**: 學習 pool.lua 的實現

## 版本 1.1 (2025-06-15) - 初始修正版本

### ✅ 基本功能實現
- 家族建設申請系統
- 家族倉庫系統 (道具和寵物)
- 權限控制系統
- 數據庫設計

### ⚠️ 已知問題 (已在後續版本修正)
- 使用錯誤的 dofile 調用方式
- 使用錯誤的家族變數名稱
- 包含不必要的隊伍檢查
- NPC 創建問題

## 版本 1.0 (2025-06-15) - 初始版本

### ✅ 核心功能
- 基本的家族建設申請
- 簡單的權限控制
- 基礎的數據庫結構

### ⚠️ 限制
- 功能不完整
- 存在多個技術問題
- 缺乏完整的測試

---

## 重要里程碑

### 🎯 版本 1.5 的重要性
這是文檔結構的重大改進版本：
- ✅ 通用 mylua 知識與具體項目分離
- ✅ 便於未來添加其他項目
- ✅ AI 助手可以快速找到對應文檔
- ✅ 符合軟件工程的最佳實踐

### 🎯 版本 1.4 的重要性
這是第一個可以實際部署使用的版本：
- ✅ NPC 能正確創建和響應
- ✅ 窗口事件能正確處理
- ✅ 所有函數調用都經過源碼驗證
- ✅ 文檔完整且組織良好

### 🔍 開發過程中的重要發現
1. **NPC 創建的重要性**: 沒有 NPC 就沒有事件處理
2. **源碼檢查的必要性**: 避免使用不存在的函數或變數
3. **編碼問題的影響**: Big5 編碼對中文顯示的重要性
4. **文檔組織的價值**: 統一的文檔資料夾便於維護
5. **結構設計的重要性**: 通用知識與具體項目的分離

### 📚 學習成果
- 建立了完整的 mylua 函數檢查習慣
- 掌握了正確的 NPC 創建和事件處理模式
- 理解了 Stone Age 服務器的腳本組織結構
- 形成了系統化的開發和文檔管理流程
- 設計了可擴展的文檔結構

---

**維護者**: Augment Agent  
**項目狀態**: 可部署使用  
**文檔狀態**: 結構優化完成  
**下一步計劃**: 添加家族升級系統和更多功能
