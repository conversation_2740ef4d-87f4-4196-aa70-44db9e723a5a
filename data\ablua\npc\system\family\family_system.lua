--=============================================
-- 家族建設系統 (Family Construction System)
-- 功能: 家族建設申請、管理、倉庫系統
-- 作者: Augment Agent
-- 版本: 1.0
-- 說明: 使用遊戲內建家族系統，族長可申請建設，族員可使用倉庫
--=============================================

-- 注意：不需要引入 utils.lua，因為本腳本沒有使用到其中的函數

-- 全域配置表 (必須使用 local)
local family_config = {}
local family_state = {}

-- 配置函數，由 main() 調用
function data()
    -- ===== 所有全域變數都要在這裡設定 =====
    
    -- 家族建設申請條件
    family_config.REQUIRED_REPUTATION = 500000  -- 需要聲望
    family_config.REQUIRED_DIAMOND = 800        -- 需要鑽石
    family_config.REQUIRED_PEARL = 800          -- 需要珍珠
    family_config.REQUIRED_COPPER = 800         -- 需要銅條
    family_config.REQUIRED_IRON = 800           -- 需要鐵條
    
    -- 道具編號 (參考常見道具ID，可根據實際遊戲調整)
    family_config.ITEM_DIAMOND = 31195          -- 鑽石道具編號 (參考donate.lua)
    family_config.ITEM_PEARL = 31755            -- 珍珠道具編號 (參考donate.lua)
    family_config.ITEM_COPPER = 31180           -- 銅條道具編號 (參考team_dungeon.lua)
    family_config.ITEM_IRON = 31181             -- 鐵條道具編號 (參考team_dungeon.lua)
    
    -- 家族倉庫設定
    family_config.WAREHOUSE_SLOTS = 30          -- 倉庫格數
    family_config.INITIAL_FAMILY_LEVEL = 1      -- 初始家族等級
    
    -- NPC 設定
    family_config.NPC_NAME = "家族管理員"
    family_config.NPC_METAMO = 101813
    family_config.NPC_MAP = 1000
    family_config.NPC_X = 81
    family_config.NPC_Y = 42
    family_config.NPC_DIR = 4
    
    -- 初始化隨機種子
    math.randomseed(tostring(os.time()):reverse():sub(1, 7))
end

-- 檢查玩家家族狀態 (使用遊戲內建的家族系統)
-- 已檢查 mylua/charbase.c 和 include/char_base.h 確認正確用法
function checkPlayerFamilyStatus(talkerindex)
    local family_status = char.getInt(talkerindex, "家族地位")  -- CHAR_FMLEADERFLAG
    local family_index = char.getInt(talkerindex, "家族索引")   -- CHAR_FMINDEX

    -- family_status 對應 FMMEMBER 常數 (已檢查 include/char_base.h):
    -- FMMEMBER_NONE = -1     (沒有家族)
    -- FMMEMBER_MEMBER = 1    (家族成員)
    -- FMMEMBER_APPLY = 2     (申請入族)
    -- FMMEMBER_LEADER = 3    (族長)
    -- FMMEMBER_ELDER = 4     (長老)
    return family_status, family_index
end

-- 檢查家族是否已有建設
function checkFamilyConstruction(family_index)
    local token = "SELECT construction_level FROM family_constructions WHERE family_index=" .. family_index
    local ret = sasql.query(token, 1)

    if ret > 0 then
        sasql.fetch_row(1)
        if sasql.data(1) ~= nil then
            return other.atoi(sasql.data(1))
        end
    end
    return 0  -- 沒有建設記錄
end

-- 檢查道具數量 (已檢查 mylua/charbase.c 確認正確用法)
function checkItemCount(talkerindex, itemid, required_count)
    local count = char.countitem(talkerindex, itemid)
    return count >= required_count, count
end

-- 消耗道具 (已檢查 mylua/npcbase.c 確認正確用法)
function consumeItem(talkerindex, itemid, required_count)
    local current_count = char.countitem(talkerindex, itemid)
    if current_count >= required_count then
        -- 使用 npc.DelItemNum 刪除指定數量的道具
        -- 參數格式: "itemid,count"
        local result = npc.DelItemNum(talkerindex, itemid .. "," .. required_count)
        return result == 1  -- 成功返回 1
    end
    return false
end

-- 檢查申請條件
function checkApplicationRequirements(talkerindex)
    local errors = {}

    -- 檢查是否有家族
    local family_status, family_index = checkPlayerFamilyStatus(talkerindex)
    if family_status == -1 then  -- FMMEMBER_NONE
        table.insert(errors, "你必須先加入家族才能申請建設")
        return false, errors
    end

    -- 檢查是否為族長
    if family_status ~= 3 then  -- FMMEMBER_LEADER
        table.insert(errors, "只有族長才能申請家族建設")
        return false, errors
    end

    -- 檢查家族是否已有建設
    local construction_level = checkFamilyConstruction(family_index)
    if construction_level > 0 then
        table.insert(errors, "家族已有建設 (等級: " .. construction_level .. ")")
        return false, errors
    end
    
    -- 檢查聲望
    local current_reputation = char.getInt(talkerindex, "聲望") or 0
    if current_reputation < family_config.REQUIRED_REPUTATION then
        table.insert(errors, "聲望不足 (需要: " .. family_config.REQUIRED_REPUTATION .. ", 目前: " .. current_reputation .. ")")
    end
    
    -- 檢查道具
    local diamond_ok, diamond_count = checkItemCount(talkerindex, family_config.ITEM_DIAMOND, family_config.REQUIRED_DIAMOND)
    if not diamond_ok then
        table.insert(errors, "鑽石不足 (需要: " .. family_config.REQUIRED_DIAMOND .. ", 目前: " .. diamond_count .. ")")
    end
    
    local pearl_ok, pearl_count = checkItemCount(talkerindex, family_config.ITEM_PEARL, family_config.REQUIRED_PEARL)
    if not pearl_ok then
        table.insert(errors, "珍珠不足 (需要: " .. family_config.REQUIRED_PEARL .. ", 目前: " .. pearl_count .. ")")
    end
    
    local copper_ok, copper_count = checkItemCount(talkerindex, family_config.ITEM_COPPER, family_config.REQUIRED_COPPER)
    if not copper_ok then
        table.insert(errors, "銅條不足 (需要: " .. family_config.REQUIRED_COPPER .. ", 目前: " .. copper_count .. ")")
    end
    
    local iron_ok, iron_count = checkItemCount(talkerindex, family_config.ITEM_IRON, family_config.REQUIRED_IRON)
    if not iron_ok then
        table.insert(errors, "鐵條不足 (需要: " .. family_config.REQUIRED_IRON .. ", 目前: " .. iron_count .. ")")
    end
    
    return #errors == 0, errors
end

-- 顯示主選單
function showMainMenu(meindex, talkerindex)
    local family_status, family_index = checkPlayerFamilyStatus(talkerindex)
    local token = "4                  【" .. char.getChar(meindex, "姓名") .. "】\\n"

    if family_status == -1 then  -- FMMEMBER_NONE
        -- 沒有家族
        token = token .. "歡迎來到家族建設系統！\\n\\n"
        token = token .. "你還沒有加入家族\\n"
        token = token .. "請先加入家族後再來申請建設\\n\\n"
        token = token .. "【查看建設條件】"
    else
        -- 有家族
        local construction_level = checkFamilyConstruction(family_index)
        token = token .. "歡迎來到家族建設系統！\\n\\n"

        if construction_level > 0 then
            -- 已有建設
            token = token .. "家族建設等級：Lv." .. construction_level .. "\\n\\n"
            if family_status == 3 then  -- FMMEMBER_LEADER
                -- 族長可以使用所有功能
                token = token .. "【家族倉庫】\\n"
                token = token .. "【建設資訊】\\n"
                token = token .. "【升級建設】"
            else
                -- 族員只能使用倉庫和查看資訊
                token = token .. "【家族倉庫】\\n"
                token = token .. "【建設資訊】\\n"
                token = token .. "【升級建設】(族長專用)"
            end
        else
            -- 沒有建設
            if family_status == 3 then  -- FMMEMBER_LEADER
                -- 族長可以申請
                token = token .. "家族尚未建設\\n\\n"
                token = token .. "【申請家族建設】\\n"
                token = token .. "【查看建設條件】"
            else
                -- 非族長只能查看
                token = token .. "家族尚未建設\\n"
                token = token .. "只有族長可以申請建設\\n\\n"
                token = token .. "【查看建設條件】"
            end
        end
    end

    lssproto.windows(talkerindex, "對話窗", "確定", 0, char.getWorkInt(meindex, "對象"), token)
end

-- 顯示申請條件
function showApplicationRequirements(meindex, talkerindex)
    local token = "4                  【家族建設申請條件】\\n\\n"
    token = token .. "基本條件：\\n"
    token = token .. "‧ 必須已加入家族\\n"
    token = token .. "‧ 必須是族長身份\\n"
    token = token .. "‧ 家族尚未建設\\n\\n"
    token = token .. "消耗資源：\\n"
    token = token .. "‧ 聲望：" .. family_config.REQUIRED_REPUTATION .. "\\n"
    token = token .. "‧ 鑽石：" .. family_config.REQUIRED_DIAMOND .. " 個\\n"
    token = token .. "‧ 珍珠：" .. family_config.REQUIRED_PEARL .. " 個\\n"
    token = token .. "‧ 銅條：" .. family_config.REQUIRED_COPPER .. " 個\\n"
    token = token .. "‧ 鐵條：" .. family_config.REQUIRED_IRON .. " 個\\n\\n"
    token = token .. "家族福利：\\n"
    token = token .. "‧ 家族倉庫：" .. family_config.WAREHOUSE_SLOTS .. " 格\\n"
    token = token .. "‧ 初始等級：Lv." .. family_config.INITIAL_FAMILY_LEVEL

    lssproto.windows(talkerindex, "對話窗", "確定", 1, char.getWorkInt(meindex, "對象"), token)
end

-- 顯示申請確認
function showApplicationConfirm(meindex, talkerindex)
    local family_status, family_index = checkPlayerFamilyStatus(talkerindex)
    local current_reputation = char.getInt(talkerindex, "聲望") or 0
    local family_name = char.getChar(talkerindex, "家族") or "未知"

    local token = "4                  【確認家族建設申請】\\n\\n"
    token = token .. "請確認以下資訊：\\n\\n"
    token = token .. "申請人：" .. char.getChar(talkerindex, "姓名") .. "\\n"
    token = token .. "家族名稱：" .. family_name .. "\\n"
    token = token .. "家族索引：" .. family_index .. "\\n"
    token = token .. "目前聲望：" .. current_reputation .. "\\n\\n"
    token = token .. "將消耗以下資源：\\n"
    token = token .. "‧ 聲望：" .. family_config.REQUIRED_REPUTATION .. "\\n"
    token = token .. "‧ 鑽石：" .. family_config.REQUIRED_DIAMOND .. " 個\\n"
    token = token .. "‧ 珍珠：" .. family_config.REQUIRED_PEARL .. " 個\\n"
    token = token .. "‧ 銅條：" .. family_config.REQUIRED_COPPER .. " 個\\n"
    token = token .. "‧ 鐵條：" .. family_config.REQUIRED_IRON .. " 個\\n\\n"
    token = token .. "確定要申請家族建設嗎？"

    lssproto.windows(talkerindex, "對話窗", "確定|取消", 2, char.getWorkInt(meindex, "對象"), token)
end

-- 創建家族建設
function createFamilyConstruction(talkerindex)
    -- 再次檢查條件（防止作弊）
    local can_apply, errors = checkApplicationRequirements(talkerindex)
    if not can_apply then
        local error_msg = "[系統]: 申請失敗，條件不符：\\n"
        for i, error in ipairs(errors) do
            error_msg = error_msg .. "‧ " .. error .. "\\n"
        end
        char.TalkToCli(talkerindex, -1, error_msg, "紅色")
        return false
    end

    -- 消耗資源
    local success = true

    -- 消耗聲望
    local current_reputation = char.getInt(talkerindex, "聲望") or 0
    char.setInt(talkerindex, "聲望", current_reputation - family_config.REQUIRED_REPUTATION)

    -- 消耗道具
    if not consumeItem(talkerindex, family_config.ITEM_DIAMOND, family_config.REQUIRED_DIAMOND) then
        success = false
    end
    if not consumeItem(talkerindex, family_config.ITEM_PEARL, family_config.REQUIRED_PEARL) then
        success = false
    end
    if not consumeItem(talkerindex, family_config.ITEM_COPPER, family_config.REQUIRED_COPPER) then
        success = false
    end
    if not consumeItem(talkerindex, family_config.ITEM_IRON, family_config.REQUIRED_IRON) then
        success = false
    end

    if not success then
        char.TalkToCli(talkerindex, -1, "[系統]: 道具消耗失敗，請重試", "紅色")
        return false
    end

    -- 獲取家族資訊
    local family_status, family_index = checkPlayerFamilyStatus(talkerindex)
    local leader_name = char.getChar(talkerindex, "姓名")
    local create_time = os.date("%Y-%m-%d %H:%M:%S")

    -- 創建家族建設記錄
    local create_construction_sql = "INSERT INTO family_constructions (family_index, leader_name, construction_level, create_time, warehouse_slots) VALUES ("
        .. family_index .. ", '" .. leader_name .. "', "
        .. family_config.INITIAL_FAMILY_LEVEL .. ", '" .. create_time .. "', " .. family_config.WAREHOUSE_SLOTS .. ")"

    local ret = sasql.query(create_construction_sql, 0)
    if ret < 0 then
        char.TalkToCli(talkerindex, -1, "[系統]: 家族建設創建失敗，請聯繫管理員", "紅色")
        return false
    end

    -- 初始化家族倉庫
    local init_warehouse_sql = "INSERT INTO family_warehouse (family_index, slot_count) VALUES (" .. family_index .. ", " .. family_config.WAREHOUSE_SLOTS .. ")"
    sasql.query(init_warehouse_sql, 0)

    char.TalkToCli(talkerindex, -1, "[系統]: 家族建設申請成功\！", "綠色")
    char.TalkToCli(talkerindex, -1, "[系統]: 建設等級：Lv." .. family_config.INITIAL_FAMILY_LEVEL .. "，倉庫格數：" .. family_config.WAREHOUSE_SLOTS, "綠色")

    return true
end

-- 顯示建設資訊
function showConstructionInfo(meindex, talkerindex)
    local family_status, family_index = checkPlayerFamilyStatus(talkerindex)
    if family_status == -1 then  -- FMMEMBER_NONE
        char.TalkToCli(talkerindex, -1, "[系統]: 你還沒有加入家族", "紅色")
        return
    end

    local construction_level = checkFamilyConstruction(family_index)
    if construction_level == 0 then
        char.TalkToCli(talkerindex, -1, "[系統]: 家族尚未建設", "紅色")
        return
    end

    -- 獲取建設詳細資訊
    local token = "SELECT leader_name, create_time, warehouse_slots FROM family_constructions WHERE family_index=" .. family_index
    local ret = sasql.query(token, 1)

    local leader_name = "未知"
    local create_time = "未知"
    local warehouse_slots = 30

    if ret > 0 then
        sasql.fetch_row(1)
        if sasql.data(1) ~= nil then
            leader_name = sasql.data(1)
            create_time = sasql.data(2)
            warehouse_slots = other.atoi(sasql.data(3))
        end
    end

    local info_token = "4                  【家族建設資訊】\\n\\n"
    info_token = info_token .. "家族索引：" .. family_index .. "\\n"
    info_token = info_token .. "建設等級：Lv." .. construction_level .. "\\n"
    info_token = info_token .. "建設族長：" .. leader_name .. "\\n"
    info_token = info_token .. "建設時間：" .. create_time .. "\\n"
    info_token = info_token .. "倉庫格數：" .. warehouse_slots .. " 格\\n\\n"
    info_token = info_token .. "你的家族地位：" .. getFamilyStatusName(family_status)

    lssproto.windows(talkerindex, "對話窗", "確定", 3, char.getWorkInt(meindex, "對象"), info_token)
end

-- 獲取家族地位名稱 (已檢查 include/char_base.h 確認 FMMEMBER 常數)
function getFamilyStatusName(family_status)
    if family_status == -1 then      -- FMMEMBER_NONE
        return "無家族"
    elseif family_status == 1 then   -- FMMEMBER_MEMBER
        return "家族成員"
    elseif family_status == 2 then   -- FMMEMBER_APPLY
        return "申請入族"
    elseif family_status == 3 then   -- FMMEMBER_LEADER
        return "族長"
    elseif family_status == 4 then   -- FMMEMBER_ELDER
        return "長老"
    else
        return "未知"
    end
end

-- 顯示家族倉庫選單
function showFamilyWarehouseMenu(meindex, talkerindex)
    local family_status, family_index = checkPlayerFamilyStatus(talkerindex)
    if family_status == -1 then  -- FMMEMBER_NONE
        char.TalkToCli(talkerindex, -1, "[系統]: 你還沒有加入家族", "紅色")
        return
    end

    -- 檢查家族是否已建設
    if checkFamilyConstruction(family_index) == 0 then
        char.TalkToCli(talkerindex, -1, "[系統]: 家族尚未建設，無法使用倉庫", "紅色")
        return
    end

    local token = "4                  【家族倉庫系統】\\n\\n"
    token = token .. "歡迎使用家族倉庫！\\n"
    token = token .. "家族成員可以共享道具和寵物\\n\\n"
    token = token .. "【道具倉庫】\\n"
    token = token .. "【寵物倉庫】\\n"
    token = token .. "【返回主選單】"

    lssproto.windows(talkerindex, "對話窗", "確定", 4, char.getWorkInt(meindex, "對象"), token)
end

-- NPC 對話函數
function Talked(meindex, talkerindex, szMes, color)
    if npc.isFaceToFace(meindex, talkerindex) ~= 1 then return end

    showMainMenu(meindex, talkerindex)
end

-- NPC 視窗對話函數
function WindowTalked(meindex, talkerindex, seqno, select, data)
    if npc.isFaceToFace(meindex, talkerindex) ~= 1 then return end

    if select == 2 then -- 取消
        return
    end

    local num = other.atoi(data)
    local family_status, family_index = checkPlayerFamilyStatus(talkerindex)

    if seqno == 0 then -- 主選單
        local construction_level = checkFamilyConstruction(family_index)

        if family_status == -1 then  -- FMMEMBER_NONE
            -- 沒有家族
            if num == 1 then -- 查看建設條件
                showApplicationRequirements(meindex, talkerindex)
            end
        elseif construction_level > 0 then
            -- 已有建設
            if num == 1 then -- 家族倉庫 (所有家族成員都可以使用)
                showFamilyWarehouseMenu(meindex, talkerindex)
            elseif num == 2 then -- 建設資訊 (所有家族成員都可以查看)
                showConstructionInfo(meindex, talkerindex)
            elseif num == 3 then -- 升級建設 (只有族長可以使用)
                if family_status == 3 then  -- FMMEMBER_LEADER
                    char.TalkToCli(talkerindex, -1, "[系統]: 升級功\能開發中...", "黃色")
                else
                    char.TalkToCli(talkerindex, -1, "[系統]: 只有族長才能升級建設", "紅色")
                end
            end
        else
            -- 沒有建設
            if family_status == 3 then  -- FMMEMBER_LEADER
                -- 族長可以申請
                if num == 1 then -- 申請家族建設
                    local can_apply, errors = checkApplicationRequirements(talkerindex)
                    if can_apply then
                        showApplicationConfirm(meindex, talkerindex)
                    else
                        local error_msg = "[系統]: 申請條件不符：\\n"
                        for i, error in ipairs(errors) do
                            error_msg = error_msg .. "‧ " .. error .. "\\n"
                        end
                        char.TalkToCli(talkerindex, -1, error_msg, "紅色")
                    end
                elseif num == 2 then -- 查看建設條件
                    showApplicationRequirements(meindex, talkerindex)
                end
            else
                -- 非族長只能查看
                if num == 1 then -- 查看建設條件
                    showApplicationRequirements(meindex, talkerindex)
                end
            end
        end
    elseif seqno == 1 then -- 查看建設條件後返回
        showMainMenu(meindex, talkerindex)
    elseif seqno == 2 then -- 申請確認
        if num == 1 then -- 確定申請
            createFamilyConstruction(talkerindex)
        end
        showMainMenu(meindex, talkerindex)
    elseif seqno == 3 then -- 建設資訊後返回
        showMainMenu(meindex, talkerindex)
    elseif seqno == 4 then -- 家族倉庫選單
        if num == 1 then -- 道具倉庫
            other.CallFunction("openFamilyItemStorage", "./familypool.lua", {talkerindex, meindex})
        elseif num == 2 then -- 寵物倉庫
            other.CallFunction("openFamilyPetStorage", "./familypool.lua", {talkerindex, meindex})
        elseif num == 3 then -- 返回主選單
            showMainMenu(meindex, talkerindex)
        end
    elseif seqno == 8 then -- 倉庫窗口事件 (來自 familypool.lua 的窗口)
        -- 處理倉庫窗口的存取操作
        handleWarehouseWindowEvent(talkerindex, select, data)
    end
end

-- 處理倉庫窗口事件
function handleWarehouseWindowEvent(talkerindex, select, data)
    local family_status, family_index = checkPlayerFamilyStatus(talkerindex)
    if family_status == -1 then
        char.TalkToCli(talkerindex, -1, "[系統]: 你還沒有加入家族", "紅色")
        return
    end

    local num = other.atoi(data)

    -- 道具倉庫操作 (窗口ID 1103)
    if select == 1 then  -- 取出道具
        if num > 0 and num <= 30 then  -- 倉庫格數
            other.CallFunction("drawFamilyItem", "./familypool.lua", {talkerindex, num})
        end
    elseif select == 2 then  -- 存入道具
        if num > 8 and num <= 23 then  -- 背包位置
            other.CallFunction("saveFamilyItem", "./familypool.lua", {talkerindex, num - 1})
        end
    end

    -- 寵物倉庫操作 (窗口ID 1102)
    if select == 1 then  -- 取出寵物
        if num > 0 and num <= 30 then  -- 倉庫格數
            other.CallFunction("drawFamilyPet", "./familypool.lua", {talkerindex, num})
        end
    elseif select == 2 then  -- 存入寵物
        if num > 0 and num <= 5 then  -- 寵物位置
            other.CallFunction("saveFamilyPet", "./familypool.lua", {talkerindex, num - 1})
        end
    end
end

-- 創建家族建設管理員 NPC
function CreateFamilySystemNpc(name, metamo, floor, x, y, dir)
    local npcindex = npc.CreateNpc(name, metamo, floor, x, y, dir)
    if npcindex >= 0 then
        -- 設置對話函數指針
        char.setFunctionPointer(npcindex, "對話函數", "Talked", "")
        -- 設置窗口對話函數指針
        char.setFunctionPointer(npcindex, "視窗函數", "WindowTalked", "")
        return npcindex
    end
    return -1
end

-- 主函數
function main()
    data()

    -- 創建家族建設管理員 NPC
    -- 參數: (名稱, 外觀編號, 地圖, X座標, Y座標, 方向)
    local family_npc = CreateFamilySystemNpc(
        family_config.NPC_NAME,     -- "家族管理員"
        family_config.NPC_METAMO,   -- 101813
        family_config.NPC_MAP,      -- 1000
        family_config.NPC_X,        -- 100
        family_config.NPC_Y,        -- 100
        family_config.NPC_DIR       -- 6
    )

    if family_npc >= 0 then
        print("[家族系統] 家族建設管理員 NPC 創建成功\，索引: " .. family_npc)
    else
        print("[家族系統] 家族建設管理員 NPC 創建失敗")
    end
end
