# 家族建設系統

一個完整的 Stone Age 家族建設和倉庫管理系統，基於遊戲內建家族系統擴展功能。

## 📋 項目概述

### 核心功能
- **家族建設申請**: 族長可申請家族建設，提供共享設施
- **家族倉庫系統**: 30格道具倉庫 + 30格寵物倉庫
- **權限管理**: 族長管理建設，族員使用設施
- **安全機制**: 禁止存儲特殊道具，完整的錯誤處理

### 技術特色
- 基於遊戲內建家族系統，無需創建新的家族機制
- 使用正確的 mylua 函數，經過源碼驗證
- 完整的 NPC 創建和事件處理
- 參考 pool.lua 的倉庫實現模式

## 🚀 快速開始

### 系統要求
- Stone Age 服務器
- mylua 系統支持
- MySQL 數據庫

### 部署步驟
1. 閱讀 [系統概述](./system_overview.md) 了解功能
2. 按照 [部署指南](./deployment_guide.md) 進行部署
3. 參考 [數據庫設計](./database_schema.md) 創建表結構

## 📁 文檔結構

```
family_system/
├── README.md                  # 本文檔 (項目概述)
├── system_overview.md         # 系統功能詳細說明
├── deployment_guide.md        # 完整部署指南
├── database_schema.md         # 數據庫設計文檔
├── troubleshooting.md         # 故障排除指南
└── changelog.md               # 版本更新記錄
```

## 📚 文檔導航

### 📖 了解系統
- **[系統概述](./system_overview.md)** - 詳細的功能介紹和技術架構
- **[數據庫設計](./database_schema.md)** - 完整的數據庫結構說明

### 🚀 部署使用
- **[部署指南](./deployment_guide.md)** - 從安裝到配置的完整步驟
- **[故障排除](./troubleshooting.md)** - 常見問題和解決方案

### 📝 開發維護
- **[更新日誌](./changelog.md)** - 版本歷史和重要修正記錄

## 🎯 系統狀態

### ✅ 當前版本: 1.4
- **狀態**: 可部署使用
- **最後更新**: 2025-06-18
- **重要特色**: 完整的 NPC 創建、窗口事件處理、源碼驗證

### 🔧 核心文件
```
data/ablua/npc/system/
├── family_system.lua      # 主要家族建設系統 (Big5編碼)
└── familypool.lua         # 家族倉庫函數庫 (Big5編碼)
```

### 📊 數據庫表
- `family_constructions` - 家族建設記錄
- `family_warehouse` - 家族道具倉庫
- `family_warehouse_pets` - 家族寵物倉庫

## 🏆 技術亮點

### 1. 源碼驗證的函數使用
```lua
-- ✅ 已檢查 mylua/lssprotobase.c 確認正確用法
lssproto.windows(talkerindex, 1103, 8, 0, char.getWorkInt(npcindex, "對象"), data)

-- ✅ 已檢查 mylua/charbase.c 確認正確變數名稱
local family_index = char.getInt(talkerindex, "家族索引")  -- CHAR_FMINDEX
```

### 2. 正確的 NPC 創建模式
```lua
function CreateFamilySystemNpc(name, metamo, floor, x, y, dir)
    local npcindex = npc.CreateNpc(name, metamo, floor, x, y, dir)
    if npcindex >= 0 then
        char.setFunctionPointer(npcindex, "對話事件", "Talked", "")
        char.setFunctionPointer(npcindex, "窗口事件", "WindowTalked", "")
        return npcindex
    end
    return -1
end
```

### 3. 清晰的權限控制
```lua
if family_status == 3 then  -- FMMEMBER_LEADER (族長)
    -- 申請建設、升級建設、使用倉庫、查看資訊
elseif family_status >= 1 then  -- 家族成員
    -- 使用倉庫、查看資訊
else
    -- 只能查看建設條件
end
```

## 🔍 開發經驗

### 重要發現
1. **NPC 創建的關鍵性**: 沒有 NPC 就沒有事件處理
2. **源碼檢查的必要性**: 避免使用不存在的函數或變數
3. **窗口函數參數**: 必須使用正確的對象索引
4. **編碼問題**: Big5 編碼對中文顯示的重要性

### 學習成果
- 建立了完整的 mylua 函數檢查習慣
- 掌握了正確的 NPC 創建和事件處理模式
- 理解了 Stone Age 服務器的腳本組織結構
- 形成了系統化的開發和文檔管理流程

## 🛠️ 擴展計劃

### 短期計劃
- 家族建設升級系統
- 更多倉庫功能 (分類、搜索)
- 家族成員權限細化

### 長期計劃
- 家族技能系統
- 家族戰爭功能
- 家族商店系統

## 🤝 貢獻指南

### 開發規範
- 遵循 [mylua 最佳實踐](../../mylua_reference/best_practices.md)
- 使用 [編程參考](../../mylua_reference/coding_reference.md) 驗證函數用法
- 參考 [常見錯誤](../../mylua_reference/common_errors.md) 避免問題

### 文檔更新
- 新增功能時更新系統概述
- 修正問題時更新故障排除指南
- 版本發布時更新更新日誌

## 📞 支持

### 問題報告
1. 查看 [故障排除指南](./troubleshooting.md)
2. 檢查 [常見錯誤](../../mylua_reference/common_errors.md)
3. 參考 [部署指南](./deployment_guide.md) 確認配置

### 技術支持
- 參考 [mylua 開發文檔](../../mylua_reference/)
- 查看現有系統腳本 (pool.lua, donate.lua)
- 檢查 mylua 源碼確認函數用法

---

**項目版本**: 1.4  
**作者**: Augment Agent  
**最後更新**: 2025-06-18  
**狀態**: 可部署使用
