NPCCREATE
# generated by npcgen.perl which is made by ringo
#
#
# infile : data/npc/king/edenpark.gen
#line 3
{
floorid=7001
borncorner=20,59,20,59
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|30|95
}
# between
{
floorid=7000
borncorner=30,95,30,95
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7001|20|59
}
#line 4
{
floorid=7001
borncorner=21,59,21,59
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|31|95
}
# between
{
floorid=7000
borncorner=31,95,31,95
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7001|21|59
}
#line 5
{
floorid=7001
borncorner=22,59,22,59
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|32|95
}
# between
{
floorid=7000
borncorner=32,95,32,95
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7001|22|59
}
#line 7
{
floorid=7000
borncorner=70,111,70,111
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7002|0|39
}
# between
{
floorid=7002
borncorner=0,39,0,39
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|70|111
}
#line 8
{
floorid=7000
borncorner=70,112,70,112
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7002|0|40
}
# between
{
floorid=7002
borncorner=0,40,0,40
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|70|112
}
#line 9
{
floorid=7000
borncorner=70,113,70,113
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7002|0|41
}
# between
{
floorid=7002
borncorner=0,41,0,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|70|113
}
#line 12
{
floorid=7002
borncorner=42,27,42,27
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7003|14|29
}
#line 13
{
floorid=7002
borncorner=43,27,43,27
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7003|15|29
}
#line 18
{
floorid=7003
borncorner=14,29,14,29
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7002|42|28
}
#line 19
{
floorid=7003
borncorner=15,29,15,29
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7002|43|28
}
#line 24
{
floorid=7000
borncorner=87,134,87,134
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7005|0|25
}
# between
{
floorid=7005
borncorner=0,25,0,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|87|134
}
#line 25
{
floorid=7000
borncorner=87,135,87,135
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7005|0|26
}
# between
{
floorid=7005
borncorner=0,26,0,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|87|135
}
#line 33
{
floorid=7000
borncorner=168,161,168,161
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7009|7|15
}
# between
{
floorid=7009
borncorner=7,15,7,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|168|161
}
#line 34
{
floorid=7000
borncorner=169,161,169,161
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7009|8|15
}
# between
{
floorid=7009
borncorner=8,15,8,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|169|161
}
#line 36
{
floorid=7000
borncorner=154,142,154,142
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7010|8|17
}
# between
{
floorid=7010
borncorner=8,17,8,17
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|154|142
}
#line 37
{
floorid=7000
borncorner=155,142,155,142
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7010|9|17
}
# between
{
floorid=7010
borncorner=9,17,9,17
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|155|142
}
#line 39
{
floorid=7000
borncorner=140,142,140,142
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7011|4|14
}
# between
{
floorid=7011
borncorner=4,14,4,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|140|142
}
#line 40
{
floorid=7000
borncorner=141,142,141,142
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7011|5|14
}
# between
{
floorid=7011
borncorner=5,14,5,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|141|142
}
#line 42
{
floorid=7000
borncorner=38,115,38,115
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7012|13|24
}
# between
{
floorid=7012
borncorner=13,24,13,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|38|115
}
#line 43
{
floorid=7000
borncorner=39,115,39,115
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7012|14|24
}
# between
{
floorid=7012
borncorner=14,24,14,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|39|115
}
#line 45
{
floorid=7000
borncorner=47,164,47,164
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7013|3|19
}
# between
{
floorid=7013
borncorner=3,19,3,19
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|47|164
}
#line 46
{
floorid=7000
borncorner=48,164,48,164
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7013|4|19
}
# between
{
floorid=7013
borncorner=4,19,4,19
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|48|164
}
#line 47
{
floorid=7000
borncorner=54,165,54,165
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7013|13|19
}
# between
{
floorid=7013
borncorner=13,19,13,19
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|54|165
}
#line 48
{
floorid=7000
borncorner=55,165,55,165
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7013|14|19
}
# between
{
floorid=7013
borncorner=14,19,14,19
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|55|165
}
#line 49
{
floorid=7000
borncorner=61,166,61,166
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7013|24|19
}
# between
{
floorid=7013
borncorner=24,19,24,19
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|61|166
}
#line 50
{
floorid=7000
borncorner=62,166,62,166
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7013|25|19
}
# between
{
floorid=7013
borncorner=25,19,25,19
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|62|166
}
#line 52
{
floorid=7000
borncorner=64,168,64,168
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7014|0|5
}
# between
{
floorid=7014
borncorner=0,5,0,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|64|168
}
#line 53
{
floorid=7000
borncorner=64,169,64,169
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7014|0|6
}
# between
{
floorid=7014
borncorner=0,6,0,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|64|169
}
#line 54
{
floorid=7000
borncorner=64,173,64,173
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7014|0|12
}
# between
{
floorid=7014
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|64|173
}
#line 55
{
floorid=7000
borncorner=64,174,64,174
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7014|0|13
}
# between
{
floorid=7014
borncorner=0,13,0,13
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|64|174
}
#line 56
{
floorid=7000
borncorner=64,179,64,179
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7014|0|19
}
# between
{
floorid=7014
borncorner=0,19,0,19
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|64|179
}
#line 57
{
floorid=7000
borncorner=64,180,64,180
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7014|0|20
}
# between
{
floorid=7014
borncorner=0,20,0,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|64|180
}
#line 60
{
floorid=7000
borncorner=112,62,112,62
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7016|0|9
}
# between
{
floorid=7016
borncorner=0,9,0,9
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|112|62
}
#line 61
{
floorid=7000
borncorner=112,63,112,63
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7016|0|10
}
# between
{
floorid=7016
borncorner=0,10,0,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|112|63
}
#line 63
{
floorid=7000
borncorner=169,132,169,132
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7017|0|7
}
# between
{
floorid=7017
borncorner=0,7,0,7
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|169|132
}
#line 64
{
floorid=7000
borncorner=169,133,169,133
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7017|0|8
}
# between
{
floorid=7017
borncorner=0,8,0,8
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|169|133
}
#line 66
{
floorid=7000
borncorner=36,74,36,74
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7018|7|14
}
# between
{
floorid=7018
borncorner=7,14,7,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|36|74
}
#line 67
{
floorid=7000
borncorner=37,74,37,74
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7018|8|14
}
# between
{
floorid=7018
borncorner=8,14,8,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|37|74
}
#line 69
{
floorid=7000
borncorner=61,60,61,60
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7019|7|14
}
# between
{
floorid=7019
borncorner=7,14,7,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|61|60
}
#line 70
{
floorid=7000
borncorner=62,60,62,60
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7019|8|14
}
# between
{
floorid=7019
borncorner=8,14,8,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|62|60
}
#line 74
{
floorid=7000
borncorner=21,63,21,63
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60040|16|19
}
# between
{
floorid=60040
borncorner=16,19,16,19
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|21|63
}
#line 76
{
floorid=60041
borncorner=0,29,0,29
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60040|19|10
}
#line 77
{
floorid=60041
borncorner=48,34,48,34
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60042|0|17
}
# between
{
floorid=60042
borncorner=0,17,0,17
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60041|48|34
}
#line 78
{
floorid=60042
borncorner=32,49,32,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60043|16|3
}
# between
{
floorid=60043
borncorner=16,3,16,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60042|32|49
}
#line 79
{
floorid=60043
borncorner=35,34,35,34
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60044|0|6
}
# between
{
floorid=60044
borncorner=0,6,0,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60043|35|34
}
#line 80
{
floorid=60045
borncorner=0,6,0,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60043|35|34
}
