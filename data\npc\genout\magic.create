NPCCREATE
# generated by npcgen.perl which is made by ringo
#
#
# infile : data/npc/eden1/magic.gen
#line 3
{
floorid=60011
borncorner=12,15,12,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|7000|181|53
}
#line 4
{
floorid=60032
borncorner=20,5,20,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60033|20|5
}
# between
{
floorid=60033
borncorner=20,5,20,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60032|20|5
}
#line 8
{
floorid=60011
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60012|12|15
}
# between
{
floorid=60012
borncorner=12,15,12,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60011|0|12
}
#line 10
{
floorid=60012
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60016|12|15
}
# between
{
floorid=60016
borncorner=12,15,12,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60012|13|3
}
#line 11
{
floorid=60012
borncorner=21,11,21,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60017|12|15
}
# between
{
floorid=60017
borncorner=12,15,12,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60012|21|11
}
#line 12
{
floorid=60012
borncorner=12,24,12,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60018|12|15
}
# between
{
floorid=60018
borncorner=12,15,12,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60012|12|24
}
#line 13
{
floorid=60012
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60019|12|15
}
# between
{
floorid=60019
borncorner=12,15,12,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60012|0|12
}
#line 15
{
floorid=60016
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60016|12|24
}
# between
{
floorid=60016
borncorner=12,24,12,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60016|13|3
}
#line 16
{
floorid=60016
borncorner=21,11,21,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60016|0|12
}
# between
{
floorid=60016
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60016|21|11
}
#line 18
{
floorid=60017
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60017|12|24
}
# between
{
floorid=60017
borncorner=12,24,12,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60017|13|3
}
#line 19
{
floorid=60017
borncorner=21,11,21,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60017|0|12
}
# between
{
floorid=60017
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60017|21|11
}
#line 21
{
floorid=60018
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60018|12|24
}
# between
{
floorid=60018
borncorner=12,24,12,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60018|13|3
}
#line 22
{
floorid=60018
borncorner=21,11,21,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60018|0|12
}
# between
{
floorid=60018
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60018|21|11
}
#line 24
{
floorid=60019
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60019|12|24
}
# between
{
floorid=60019
borncorner=12,24,12,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60019|13|3
}
#line 25
{
floorid=60019
borncorner=21,11,21,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60019|0|12
}
# between
{
floorid=60019
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60019|21|11
}
#line 29
{
floorid=60011
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60013|12|15
}
# between
{
floorid=60013
borncorner=12,15,12,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60011|13|3
}
#line 31
{
floorid=60013
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60028|12|15
}
# between
{
floorid=60028
borncorner=12,15,12,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60013|13|3
}
#line 32
{
floorid=60013
borncorner=21,11,21,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60029|12|15
}
# between
{
floorid=60029
borncorner=12,15,12,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60013|21|11
}
#line 33
{
floorid=60013
borncorner=12,24,12,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60030|12|15
}
# between
{
floorid=60030
borncorner=12,15,12,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60013|12|24
}
#line 34
{
floorid=60013
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60031|12|15
}
# between
{
floorid=60031
borncorner=12,15,12,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60013|0|12
}
#line 36
{
floorid=60028
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60028|12|24
}
# between
{
floorid=60028
borncorner=12,24,12,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60028|13|3
}
#line 37
{
floorid=60028
borncorner=21,11,21,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60028|0|12
}
# between
{
floorid=60028
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60028|21|11
}
#line 39
{
floorid=60029
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60029|12|24
}
# between
{
floorid=60029
borncorner=12,24,12,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60029|13|3
}
#line 40
{
floorid=60029
borncorner=21,11,21,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60029|0|12
}
# between
{
floorid=60029
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60029|21|11
}
#line 42
{
floorid=60030
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60030|12|24
}
# between
{
floorid=60030
borncorner=12,24,12,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60030|13|3
}
#line 43
{
floorid=60030
borncorner=21,11,21,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60030|0|12
}
# between
{
floorid=60030
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60030|21|11
}
#line 45
{
floorid=60031
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60031|12|24
}
# between
{
floorid=60031
borncorner=12,24,12,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60031|13|3
}
#line 46
{
floorid=60031
borncorner=21,11,21,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60031|0|12
}
# between
{
floorid=60031
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60031|21|11
}
#line 50
{
floorid=60011
borncorner=21,11,21,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60014|12|15
}
# between
{
floorid=60014
borncorner=12,15,12,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60011|21|11
}
#line 52
{
floorid=60014
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60024|12|15
}
# between
{
floorid=60024
borncorner=12,15,12,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60014|13|3
}
#line 53
{
floorid=60014
borncorner=21,11,21,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60025|12|15
}
# between
{
floorid=60025
borncorner=12,15,12,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60014|21|11
}
#line 54
{
floorid=60014
borncorner=12,24,12,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60026|12|15
}
# between
{
floorid=60026
borncorner=12,15,12,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60014|12|24
}
#line 55
{
floorid=60014
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60027|12|15
}
# between
{
floorid=60027
borncorner=12,15,12,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60014|0|12
}
#line 57
{
floorid=60024
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60024|12|24
}
# between
{
floorid=60024
borncorner=12,24,12,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60024|13|3
}
#line 58
{
floorid=60024
borncorner=21,11,21,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60024|0|12
}
# between
{
floorid=60024
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60024|21|11
}
#line 60
{
floorid=60025
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60025|12|24
}
# between
{
floorid=60025
borncorner=12,24,12,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60025|13|3
}
#line 61
{
floorid=60025
borncorner=21,11,21,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60025|0|12
}
# between
{
floorid=60025
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60025|21|11
}
#line 63
{
floorid=60026
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60026|12|24
}
# between
{
floorid=60026
borncorner=12,24,12,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60026|13|3
}
#line 64
{
floorid=60026
borncorner=21,11,21,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60026|0|12
}
# between
{
floorid=60026
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60026|21|11
}
#line 66
{
floorid=60027
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60027|12|24
}
# between
{
floorid=60027
borncorner=12,24,12,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60027|13|3
}
#line 67
{
floorid=60027
borncorner=21,11,21,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60027|0|12
}
# between
{
floorid=60027
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60027|21|11
}
#line 71
{
floorid=60011
borncorner=12,24,12,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60015|12|15
}
# between
{
floorid=60015
borncorner=12,15,12,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60011|12|24
}
#line 73
{
floorid=60015
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60020|12|15
}
# between
{
floorid=60020
borncorner=12,15,12,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60015|13|3
}
#line 74
{
floorid=60015
borncorner=21,11,21,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60021|12|15
}
# between
{
floorid=60021
borncorner=12,15,12,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60015|21|11
}
#line 75
{
floorid=60015
borncorner=12,24,12,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60022|12|15
}
# between
{
floorid=60022
borncorner=12,15,12,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60015|12|24
}
#line 76
{
floorid=60015
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60023|12|15
}
# between
{
floorid=60023
borncorner=12,15,12,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60015|0|12
}
#line 78
{
floorid=60020
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60020|12|24
}
# between
{
floorid=60020
borncorner=12,24,12,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60020|13|3
}
#line 79
{
floorid=60020
borncorner=21,11,21,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60020|0|12
}
# between
{
floorid=60020
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60020|21|11
}
#line 81
{
floorid=60021
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60021|12|24
}
# between
{
floorid=60021
borncorner=12,24,12,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60021|13|3
}
#line 82
{
floorid=60021
borncorner=21,11,21,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60021|0|12
}
# between
{
floorid=60021
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60021|21|11
}
#line 84
{
floorid=60022
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60022|12|24
}
# between
{
floorid=60022
borncorner=12,24,12,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60022|13|3
}
#line 85
{
floorid=60022
borncorner=21,11,21,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60022|0|12
}
# between
{
floorid=60022
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60022|21|11
}
#line 87
{
floorid=60023
borncorner=13,3,13,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60023|12|24
}
# between
{
floorid=60023
borncorner=12,24,12,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60023|13|3
}
#line 88
{
floorid=60023
borncorner=21,11,21,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60023|0|12
}
# between
{
floorid=60023
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|60023|21|11
}
