NPCCREATE
# generated by npcgen.perl which is made by ringo
#
#
# infile : data/npc/king/deadroad.gen
#line 2
{
floorid=32020
borncorner=20,8,20,8
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|225|13
}
#line 3
{
floorid=32020
borncorner=19,7,19,7
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|225|13
}
#line 4
{
floorid=32020
borncorner=20,7,20,7
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|225|13
}
#line 5
{
floorid=32020
borncorner=21,7,21,7
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|225|13
}
#line 7
{
floorid=32020
borncorner=19,6,19,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|225|13
}
#line 8
{
floorid=32020
borncorner=20,6,20,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|225|13
}
#line 9
{
floorid=32020
borncorner=21,6,21,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|225|13
}
#line 11
{
floorid=32020
borncorner=19,5,19,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 12
{
floorid=32020
borncorner=20,5,20,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 13
{
floorid=32020
borncorner=21,5,21,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 15
{
floorid=32021
borncorner=28,59,28,59
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|32020|20|10
}
#line 16
{
floorid=32021
borncorner=29,59,29,59
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|32020|21|10
}
#line 19
{
floorid=5511
borncorner=0,25,0,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5510|49|16
}
#line 20
{
floorid=5511
borncorner=0,26,0,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5510|49|17
}
#line 22
{
floorid=5510
borncorner=59,16,59,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 23
{
floorid=5510
borncorner=59,17,59,17
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 27
{
floorid=21214
borncorner=30,33,30,33
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 28
{
floorid=21214
borncorner=30,34,30,34
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 29
{
floorid=21214
borncorner=30,35,30,35
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 30
{
floorid=21214
borncorner=30,36,30,36
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 31
{
floorid=21214
borncorner=30,37,30,37
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 32
{
floorid=21214
borncorner=30,38,30,38
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 33
{
floorid=21214
borncorner=30,39,30,39
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 35
{
floorid=21215
borncorner=28,99,28,99
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|21214|30|43
}
#line 36
{
floorid=21215
borncorner=29,99,29,99
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|21214|31|43
}
#line 39
{
floorid=21015
borncorner=8,17,8,17
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|21014|13|17
}
#line 40
{
floorid=21015
borncorner=9,17,9,17
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|21014|14|17
}
#line 41
{
floorid=21015
borncorner=10,17,10,17
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|21014|15|17
}
#line 43
{
floorid=21014
borncorner=15,4,15,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 46
{
floorid=30691
borncorner=52,71,52,71
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 47
{
floorid=30692
borncorner=63,66,63,66
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 48
{
floorid=30693
borncorner=63,36,63,36
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
#line 49
{
floorid=30694
borncorner=59,37,59,37
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|117|180|99
}
