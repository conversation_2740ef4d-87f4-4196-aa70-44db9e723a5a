NPCCREATE
# generated by npcgen.perl which is made by ringo
#
#
# infile : data/npc/sainasu/dungeon/10402d.gen
#line 8
{
floorid=10402
borncorner=12,5,12,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10405|12|5
}
# between
{
floorid=10405
borncorner=12,5,12,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10402|12|5
}
#line 9
{
floorid=10402
borncorner=44,16,44,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10404|44|16
}
# between
{
floorid=10404
borncorner=44,16,44,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10402|44|16
}
#line 10
{
floorid=10402
borncorner=45,42,45,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10403|45|42
}
# between
{
floorid=10403
borncorner=45,42,45,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10402|45|42
}
#line 11
{
floorid=10402
borncorner=16,35,16,35
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10407|16|35
}
# between
{
floorid=10407
borncorner=16,35,16,35
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10402|16|35
}
#line 13
{
floorid=10403
borncorner=12,5,12,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10404|12|5
}
# between
{
floorid=10404
borncorner=12,5,12,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10403|12|5
}
#line 14
{
floorid=10403
borncorner=44,16,44,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10405|44|16
}
# between
{
floorid=10405
borncorner=44,16,44,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10403|44|16
}
#line 15
{
floorid=10403
borncorner=16,35,16,35
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10406|16|35
}
# between
{
floorid=10406
borncorner=16,35,16,35
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10403|16|35
}
#line 17
{
floorid=10404
borncorner=45,42,45,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10405|45|42
}
# between
{
floorid=10405
borncorner=45,42,45,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10404|45|42
}
#line 18
{
floorid=10404
borncorner=16,35,16,35
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10405|16|35
}
# between
{
floorid=10405
borncorner=16,35,16,35
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10404|16|35
}
#line 20
{
floorid=10406
borncorner=12,5,12,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10407|12|5
}
# between
{
floorid=10407
borncorner=12,5,12,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10406|12|5
}
#line 21
{
floorid=10406
borncorner=45,42,45,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10407|45|42
}
# between
{
floorid=10407
borncorner=45,42,45,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10406|45|42
}
#line 25
{
floorid=10407
borncorner=44,16,44,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10408|22|4
}
# between
{
floorid=10408
borncorner=22,4,22,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|10407|44|16
}
#line 28
{
floorid=10408
borncorner=42,40,42,40
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|1400|79|70
}
