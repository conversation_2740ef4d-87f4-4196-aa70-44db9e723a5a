# Stone Age Lua Scripting System - String Reference Guide

This document provides a comprehensive mapping of all Chinese character attribute strings, work variables, and other important string constants used in the Stone Age Lua scripting system. All strings are decoded from Big5 encoding and mapped to their corresponding C constants.

## Table of Contents
1. [Character Attributes](#character-attributes)
2. [Work Variables](#work-variables)
3. [Character Types and Modes](#character-types-and-modes)
4. [Colors and Visual Effects](#colors-and-visual-effects)
5. [Character Actions](#character-actions)
6. [Character Flags](#character-flags)
7. [Event Functions](#event-functions)
8. [Item Attributes](#item-attributes)
9. [Magic Attributes](#magic-attributes)
10. [Window and UI Types](#window-and-ui-types)
11. [Pet and Enemy Attributes](#pet-and-enemy-attributes)
12. [Usage Examples](#usage-examples)

---

## Character Attributes (角色屬性)

### Basic Character Information (基本角色信息)
| Chinese String | English Description | C Constant | Category |
|---------------|-------------------|-----------|----------|
| 名字 | Name | CHAR_NAME | String |
| 暱稱 | Nickname/Title | CHAR_OWNTITLE | String |
| 賬號 | Account | CHAR_CDKEY | String |
| 主人 | Owner | CHAR_USERPETNAME | String |
| 主人賬號 | Owner Account | CHAR_OWNERCDKEY | String |
| 主人名字 | Owner Name | CHAR_OWNERCHARANAME | String |
| 稱號 | New Name/Title | CHAR_NEWNAME | String |
| 家族 | Family | CHAR_FMNAME | String |
| Unicode | Unicode | CHAR_UNIQUECODE | String |
| 精靈列表 | Magic Table | CHAR_MAGICTBL | String |

### Character Stats (角色數值)
| Chinese String | English Description | C Constant | Category |
|---------------|-------------------|-----------|----------|
| 等級 | Level | CHAR_LV | Integer |
| HP | HP | CHAR_HP | Integer |
| MP | MP | CHAR_MP | Integer |
| 最大MP | Max MP | CHAR_MAXMP | Integer |
| 體力 | Vitality | CHAR_VITAL | Integer |
| 腕力 | Strength | CHAR_STR | Integer |
| 耐力 | Toughness | CHAR_TOUGH | Integer |
| 速度 | Dexterity | CHAR_DEX | Integer |
| 魅力 | Charm | CHAR_CHARM | Integer |
| 運氣 | Luck | CHAR_LUCK | Integer |
| 石幣 | Gold | CHAR_GOLD | Integer |
| 經驗 | Experience | CHAR_EXP | Integer |
| 總經驗 | Total Experience | CHAR_OLDEXP | Integer |
| 轉數 | Transmigration | CHAR_TRANSMIGRATION | Integer |
| 技能點 | Skill Points | CHAR_SKILLUPPOINT | Integer |
| 升級點 | Level Up Points | CHAR_LEVELUPPOINT | Integer |
| DP | Duel Points | CHAR_DUELPOINT | Integer |

### Character Attributes (角色屬性值)
| Chinese String | English Description | C Constant | Category |
|---------------|-------------------|-----------|----------|
| 地 | Earth | CHAR_EARTHAT | Integer |
| 水 | Water | CHAR_WATERAT | Integer |
| 火 | Fire | CHAR_FIREAT | Integer |
| 風 | Wind | CHAR_WINDAT | Integer |
| 暴擊 | Critical | CHAR_CRITIAL | Integer |
| 反擊 | Counter | CHAR_COUNTER | Integer |

### Position and Direction (位置和方向)
| Chinese String | English Description | C Constant | Value |
|---------------|-------------------|-----------|-------|
| 地圖號 | Floor | CHAR_FLOOR | Integer |
| 坐標X | X Coordinate | CHAR_X | Integer |
| 坐標Y | Y Coordinate | CHAR_Y | Integer |
| 方向 | Direction | CHAR_DIR | Integer |
| 北 | North | - | 0 |
| 東北 | Northeast | - | 1 |
| 東 | East | - | 2 |
| 東南 | Southeast | - | 3 |
| 南 | South | - | 4 |
| 西南 | Southwest | - | 5 |
| 西 | West | - | 6 |
| 西北 | Northwest | - | 7 |

---

## Work Variables (工作變量)

### System Work Variables (系統工作變量)
| Chinese String | English Description | C Constant | Usage |
|---------------|-------------------|-----------|-------|
| 對象 | Object Index | CHAR_WORKOBJINDEX | Object reference |
| 字號 | Font Size | CHAR_WORKFONTSIZE | UI setting |
| 戰鬥 | Battle Mode | CHAR_WORKBATTLEMODE | Battle state |
| 組隊 | Party Mode | CHAR_WORKPARTYMODE | Party state |
| 戰鬥索引 | Battle Index | CHAR_WORKBATTLEINDEX | Battle reference |
| 主人索引 | Player Index | CHAR_WORKPLAYERINDEX | Player reference |

### NPC Temporary Variables (NPC臨時變量)
| Chinese String | English Description | C Constant | Usage |
|---------------|-------------------|-----------|-------|
| NPC臨時1 | NPC Temp 1 | CHAR_NPCWORKINT1 | General purpose |
| NPC臨時2 | NPC Temp 2 | CHAR_NPCWORKINT2 | General purpose |
| NPC臨時3 | NPC Temp 3 | CHAR_NPCWORKINT3 | General purpose |
| NPC臨時4 | NPC Temp 4 | CHAR_NPCWORKINT4 | General purpose |
| NPC臨時5 | NPC Temp 5 | CHAR_NPCWORKINT5 | General purpose |
| NPC臨時6 | NPC Temp 6 | CHAR_NPCWORKINT6 | General purpose |
| NPC臨時7 | NPC Temp 7 | CHAR_NPCWORKINT7 | General purpose |
| NPC臨時8 | NPC Temp 8 | CHAR_NPCWORKINT8 | General purpose |
| NPC臨時9 | NPC Temp 9 | CHAR_NPCWORKINT9 | General purpose |
| NPC臨時10 | NPC Temp 10 | CHAR_NPCWORKINT10 | General purpose |
| NPC臨時11 | NPC Temp 11 | CHAR_NPCWORKINT11 | General purpose |
| NPC臨時12 | NPC Temp 12 | CHAR_NPCWORKINT12 | General purpose |
| NPC臨時13 | NPC Temp 13 | CHAR_NPCWORKINT13 | General purpose |

### NPC Character Variables (NPC字符變量)
| Chinese String | English Description | C Constant | Usage |
|---------------|-------------------|-----------|-------|
| NPC臨時1 | NPC Temp Char 1 | CHAR_NPCWORKCHAR1 | String storage |
| NPC臨時2 | NPC Temp Char 2 | CHAR_NPCWORKCHAR2 | String storage |
| NPC臨時3 | NPC Temp Char 3 | CHAR_NPCWORKCHAR3 | String storage |
| NPC臨時4 | NPC Temp Char 4 | CHAR_NPCWORKCHAR4 | String storage |
| NPC臨時5 | NPC Temp Char 5 | CHAR_NPCWORKCHAR5 | String storage |
| NPC臨時6 | NPC Temp Char 6 | CHAR_NPCWORKCHAR6 | String storage |

### Party Members (隊伍成員)
| Chinese String | English Description | C Constant | Usage |
|---------------|-------------------|-----------|-------|
| 隊員1 | Party Member 1 | CHAR_WORKPARTYINDEX1 | Party member reference |
| 隊員2 | Party Member 2 | CHAR_WORKPARTYINDEX2 | Party member reference |
| 隊員3 | Party Member 3 | CHAR_WORKPARTYINDEX3 | Party member reference |
| 隊員4 | Party Member 4 | CHAR_WORKPARTYINDEX4 | Party member reference |
| 隊員5 | Party Member 5 | CHAR_WORKPARTYINDEX5 | Party member reference |

### Status Effects (狀態效果)
| Chinese String | English Description | C Constant | Effect |
|---------------|-------------------|-----------|--------|
| 毒 | Poison | CHAR_WORKPOISON | Poison status |
| 麻 | Paralysis | CHAR_WORKPARALYSIS | Paralysis status |
| 睡 | Sleep | CHAR_WORKSLEEP | Sleep status |
| 石 | Stone | CHAR_WORKSTONE | Petrification |
| 酒 | Drunk | CHAR_WORKDRUNK | Drunk status |
| 混 | Confusion | CHAR_WORKCONFUSION | Confusion status |
| 虛 | Weaken | CHAR_WORKWEAKEN | Weaken status |
| 劇 | Deep Poison | CHAR_WORKDEEPPOISON | Severe poison |
| 障 | Barrier | CHAR_WORKBARRIER | Barrier status |
| 默 | No Cast | CHAR_WORKNOCAST | Cannot cast magic |
| 煞 | SARS | CHAR_WORKSARS | SARS status |

### Resistance Values (抗性數值)
| Chinese String | English Description | C Constant | Usage |
|---------------|-------------------|-----------|-------|
| 毒抗性 | Poison Resistance | CHAR_WORKMODPOISON | Poison resist |
| 麻抗性 | Paralysis Resistance | CHAR_WORKMODPARALYSIS | Paralysis resist |
| 睡抗性 | Sleep Resistance | CHAR_WORKMODSLEEP | Sleep resist |
| 石抗性 | Stone Resistance | CHAR_WORKMODSTONE | Stone resist |
| 酒抗性 | Drunk Resistance | CHAR_WORKMODDRUNK | Drunk resist |
| 混抗性 | Confusion Resistance | CHAR_WORKMODCONFUSION | Confusion resist |
| 虛抗性 | Weaken Resistance | CHAR_WORKMODWEAKEN | Weaken resist |
| 劇抗性 | Deep Poison Resistance | CHAR_WORKMODDEEPPOISON | Deep poison resist |
| 障抗性 | Barrier Resistance | CHAR_WORKMODBARRIER | Barrier resist |
| 默抗性 | No Cast Resistance | CHAR_WORKMODNOCAST | No cast resist |
| 暈抗性 | Dizzy Resistance | CHAR_WORKMODDIZZY | Dizzy resist |

---

## Character Types and Modes (角色類型和模式)

### Character Types (角色類型)
| Chinese String | English Description | C Constant | Type |
|---------------|-------------------|-----------|------|
| 無類型 | No Type | CHAR_TYPENONE | None |
| 玩家 | Player | CHAR_TYPEPLAYER | Player |
| 敵人 | Enemy | CHAR_TYPEENEMY | Enemy |
| 寵物 | Pet | CHAR_TYPEPET | Pet |
| LUA | Lua NPC | CHAR_TYPELUANPC | Script NPC |
| 幫托 | Player NPC | CHAR_TYPEPLAYERNPC | Player NPC |
| 幫寵 | Player Pet NPC | CHAR_TYPEPLAYERPETNPC | Player Pet NPC |

### Party Modes (組隊模式)
| Chinese String | English Description | C Constant | Mode |
|---------------|-------------------|-----------|------|
| 無 | None | CHAR_PARTY_NONE | Not in party |
| 隊長 | Leader | CHAR_PARTY_LEADER | Party leader |
| 隊員 | Client | CHAR_PARTY_CLIENT | Party member |

### Battle Modes (戰鬥模式)
| Chinese String | English Description | C Constant | State |
|---------------|-------------------|-----------|-------|
| 無戰鬥 | No Battle | BATTLE_CHARMODE_NONE | Not in battle |
| 戰鬥初始化 | Battle Init | BATTLE_CHARMODE_INIT | Battle starting |
| 等待戰鬥 | Wait Battle | BATTLE_CHARMODE_C_WAIT | Waiting for commands |
| 確認戰鬥 | Confirm Battle | BATTLE_CHARMODE_C_OK | Commands confirmed |
| 幫助戰鬥 | Rescue Battle | BATTLE_CHARMODE_RESCUE | Rescue mode |
| 結束戰鬥 | Final Battle | BATTLE_CHARMODE_FINAL | Battle ending |
| 觀戰初始化 | Watch Init | BATTLE_CHARMODE_WATCHINIT | Watch mode init |

### Family Status (家族狀態)
| Chinese String | English Description | C Constant | Status |
|---------------|-------------------|-----------|--------|
| 沒有家族 | No Family | FMMEMBER_NONE | Not in family |
| 申請入族 | Apply | FMMEMBER_APPLY | Applying to join |
| 族長 | Leader | FMMEMBER_LEADER | Family leader |
| 成員 | Member | FMMEMBER_MEMBER | Family member |
| 長老 | Elder | FMMEMBER_ELDER | Family elder |

---

## Colors and Visual Effects (顏色和視覺效果)

### Text Colors (文字顏色)
| Chinese String | English Description | C Constant | Color |
|---------------|-------------------|-----------|-------|
| 白色 | White | CHAR_COLORWHITE | White |
| 青色 | Cyan | CHAR_COLORCYAN | Cyan |
| 紫色 | Purple | CHAR_COLORPURPLE | Purple |
| 藍色 | Blue | CHAR_COLORBLUE | Blue |
| 黃色 | Yellow | CHAR_COLORYELLOW | Yellow |
| 綠色 | Green | CHAR_COLORGREEN | Green |
| 紅色 | Red | CHAR_COLORRED | Red |
| 灰白色 | Gray White | CHAR_COLORGRAY | Gray |
| 灰藍色 | Gray Blue | CHAR_COLORBLUE2 | Gray Blue |
| 灰綠色 | Gray Green | CHAR_COLORGREEN2 | Gray Green |

---

## Character Actions (角色動作)

### Basic Actions (基本動作)
| Chinese String | English Description | C Constant | Action |
|---------------|-------------------|-----------|--------|
| 站立 | Stand | CHAR_ACTSTAND | Standing |
| 走動 | Walk | CHAR_ACTWALK | Walking |
| 攻擊 | Attack | CHAR_ACTATTACK | Attacking |
| 投擲 | Throw | CHAR_ACTTHROW | Throwing |
| 受傷 | Damage | CHAR_ACTDAMAGE | Taking damage |
| 死亡 | Dead | CHAR_ACTDEAD | Dead |
| 魔法 | Magic | CHAR_ACTMAGIC | Casting magic |
| 道具 | Item | CHAR_ACTITEM | Using item |
| 效果 | Effect | CHAR_ACTEFFECT | Effect animation |

### Movement Actions (移動動作)
| Chinese String | English Description | C Constant | Action |
|---------------|-------------------|-----------|--------|
| 下 | Down | CHAR_ACTDOWN | Down |
| 坐 | Sit | CHAR_ACTSIT | Sitting |
| 走路 | Action Walk | CHAR_ACTACTIONWALK | Action walk |
| 旋轉 | Turn | CHAR_ACTTURN | Turning |
| 變形 | Warp | CHAR_ACTWARP | Warping |

### Emote Actions (表情動作)
| Chinese String | English Description | C Constant | Emotion |
|---------------|-------------------|-----------|---------|
| 揮手 | Wave Hand | CHAR_ACTHAND | Waving |
| 高興 | Happy | CHAR_ACTPLEASURE | Happy |
| 發怒 | Angry | CHAR_ACTANGRY | Angry |
| 悲哀 | Sad | CHAR_ACTSAD | Sad |
| 點頭 | Nod | CHAR_ACTNOD | Nodding |

### Combat Actions (戰鬥動作)
| Chinese String | English Description | C Constant | Action |
|---------------|-------------------|-----------|--------|
| 防守 | Guard | CHAR_ACTGUARD | Guarding |
| 戰鬥 | Battle | CHAR_ACTBATTLE | In battle |
| 帶隊 | Leader | CHAR_ACTLEADER | Leading |
| 觀戰 | Battle Watch | CHAR_ACTBATTLEWATCH | Watching battle |

### Special Actions (特殊動作)
| Chinese String | English Description | C Constant | Action |
|---------------|-------------------|-----------|--------|
| 交易 | Trade | CHAR_ACTTRADE | Trading |
| 天使 | Angel | CHAR_ACTANGEL | Angel summon |
| 心思 | Mind | CHAR_MIND | Mind state |
| 打開攤灘 | Open Vendor | CHAR_STREETVENDOR_OPEN | Open street vendor |
| 關閉攤灘 | Close Vendor | CHAR_STREETVENDOR_CLOSE | Close street vendor |

---

## Character Flags (角色標記)

### Visibility Flags (可見性標記)
| Chinese String | English Description | C Constant | Flag |
|---------------|-------------------|-----------|------|
| 可見 | Visible | CHAR_ISVISIBLE | Character visible |
| 透明 | Transparent | CHAR_ISTRANSPARENT | Character transparent |
| 飛行 | Flying | CHAR_ISFLYING | Character flying |

### Status Flags (狀態標記)
| Chinese String | English Description | C Constant | Flag |
|---------------|-------------------|-----------|------|
| 死亡 | Die | CHAR_ISDIE | Character dead |
| 組隊 | Party | CHAR_ISPARTY | In party |
| 決鬥 | Duel | CHAR_ISDUEL | In duel |
| 交易 | Trade | CHAR_ISTRADE | Trading |
| 名片 | Trade Card | CHAR_ISTRADECARD | Trade card |

### System Flags (系統標記)
| Chinese String | English Description | C Constant | Flag |
|---------------|-------------------|-----------|------|
| AI模式 | AI Mode | CHAR_AI_MOD | AI controlled |
| 戰鬥訊息 | Battle Message | CHAR_ISSHOWBATTLEMSG | Show battle messages |

---

## Event Functions (事件函數)

### Character Events (角色事件)
| Chinese String | English Description | C Constant | Event Type |
|---------------|-------------------|-----------|------------|
| 初始化事件 | Init Function | CHAR_INITFUNC | Initialization |
| 循環事件 | Loop Function | CHAR_LOOPFUNC | Loop/Timer |
| 死亡事件 | Dying Function | CHAR_DYINGFUNC | On death |
| 對話事件 | Talked Function | CHAR_TALKEDFUNC | On talk |
| 窗口事件 | Window Talked Function | CHAR_WINDOWTALKEDFUNC | Window dialog |
| 重疊事件 | Overlapped Function | CHAR_OVERLAPEDFUNC | On overlap |
| 戰後事件 | Battle Over Function | CHAR_BATTLEOVERDFUNC | After battle |
| 登出事件 | Logout Function | CHAR_LOGINOUTFUNC | On logout |
| 戰鬥設置事件 | Battle Set Function | CHAR_BATTLESETFUNC | Battle setup |

### Battle Events (戰鬥事件)
| Chinese String | English Description | C Constant | Event Type |
|---------------|-------------------|-----------|------------|
| 結束事件 | Finish Function | BATTLE_FINISH | Battle end |
| 逃跑事件 | Escape Function | BATTLE_ESCAPE | Battle escape |

---

## Item Attributes (道具屬性)

### Basic Item Information (基本道具信息)
| Chinese String | English Description | C Constant | Category |
|---------------|-------------------|-----------|----------|
| 序號 | ID | ITEM_ID | Integer |
| 名稱 | Name | ITEM_NAME | String |
| 顯示名 | Secret Name | ITEM_SECRETNAME | String |
| 圖號 | Image Number | ITEM_BASEIMAGENUMBER | Integer |
| 價值 | Cost | ITEM_COST | Integer |
| 售價 | Price | ITEM_PRICE | Integer |
| 類型 | Type | ITEM_TYPE | Integer |
| 等級 | Level | ITEM_LEVEL | Integer |
| 說明 | Description | ITEM_EFFECTSTRING | String |
| 字段 | Argument | ITEM_ARGUMENT | String |

### Item Properties (道具屬性)
| Chinese String | English Description | C Constant | Effect |
|---------------|-------------------|-----------|--------|
| 攻 | Attack | ITEM_MODIFYATTACK | Attack modifier |
| 防 | Defense | ITEM_MODIFYDEFENCE | Defense modifier |
| 敏 | Quick | ITEM_MODIFYQUICK | Speed modifier |
| HP | HP | ITEM_MODIFYHP | HP modifier |
| MP | MP | ITEM_MODIFYMP | MP modifier |
| 運氣 | Luck | ITEM_MODIFYLUCK | Luck modifier |
| 魅力 | Charm | ITEM_MODIFYCHARM | Charm modifier |
| 迴避 | Avoid | ITEM_MODIFYAVOID | Avoid modifier |
| 會心 | Critical | ITEM_CRITICAL | Critical rate |

### Item Requirements (道具需求)
| Chinese String | English Description | C Constant | Requirement |
|---------------|-------------------|-----------|-------------|
| 需攻 | Need Strength | ITEM_NEEDSTR | Strength requirement |
| 需敏 | Need Dexterity | ITEM_NEEDDEX | Dexterity requirement |
| 需轉 | Need Trans | ITEM_NEEDTRANS | Transmigration requirement |
| 需職業 | Need Profession | ITEM_NEEDPROFESSION | Profession requirement |

### Item Types (道具類型)
| Chinese String | English Description | C Constant | Equipment Type |
|---------------|-------------------|-----------|----------------|
| 爪 | Fist | ITEM_FIST | Fist weapon |
| 斧 | Axe | ITEM_AXE | Axe weapon |
| 棍 | Club | ITEM_CLUB | Club weapon |
| 槍 | Spear | ITEM_SPEAR | Spear weapon |
| 弓 | Bow | ITEM_BOW | Bow weapon |
| 盾 | Shield | ITEM_SHIELD | Shield |
| 盔 | Helm | ITEM_HELM | Helmet |
| 鎧 | Armor | ITEM_ARMOUR | Armor |
| 環 | Bracelet | ITEM_BRACELET | Bracelet |
| 項 | Necklace | ITEM_NECKLACE | Necklace |
| 帶 | Belt | ITEM_BELT | Belt |
| 耳環 | Earring | ITEM_EARRING | Earring |
| 鼻環 | Nose Ring | ITEM_NOSERING | Nose ring |
| 護身符 | Amulet | ITEM_AMULET | Amulet |
| 其它 | Other | ITEM_OTHER | Other items |

### Item Events (道具事件)
| Chinese String | English Description | C Constant | Event Type |
|---------------|-------------------|-----------|------------|
| 使用事件 | Use Function | ITEM_USEFUNC | On use |
| 裝上事件 | Attach Function | ITEM_ATTACHFUNC | On equip |
| 卸下事件 | Detach Function | ITEM_DETACHFUNC | On unequip |
| 丟棄事件 | Drop Function | ITEM_DROPFUNC | On drop |
| 檢起事件 | Pickup Function | ITEM_PICKUPFUNC | On pickup |
| 死亡事件 | Die Relief Function | ITEM_DIERELIFEFUNC | On character death |

---

## Magic Attributes (魔法屬性)

### Basic Magic Information (基本魔法信息)
| Chinese String | English Description | C Constant | Category |
|---------------|-------------------|-----------|----------|
| ID | ID | MAGIC_ID | Integer |
| 名字 | Name | MAGIC_NAME | String |
| 註釋 | Comment | MAGIC_COMMENT | String |
| 函數名 | Function Name | MAGIC_FUNCNAME | String |
| 選項 | Option | MAGIC_OPTION | String |
| 字段 | Field | MAGIC_FIELD | Integer |
| 目標 | Target | MAGIC_TARGET | Integer |
| 死亡目標 | Dead Target | MAGIC_TARGET_DEADFLG | Integer |
| 攻擊精靈 | Attack Magic | MAGIC_IDX | Integer |

---

## Window and UI Types (窗口和界面類型)

### Window Types (窗口類型)
| Chinese String | English Description | C Constant | Window Type |
|---------------|-------------------|-----------|-------------|
| 對話框 | Message | WINDOW_MESSAGETYPE_MESSAGE | Dialog |
| 輸入框 | Input | WINDOW_MESSAGETYPE_MESSAGEANDLINEINPUT | Input dialog |
| 選擇框 | Select | WINDOW_MESSAGETYPE_SELECT | Selection |
| 寵物框 | Pet Select | WINDOW_MESSAGETYPE_PETSELECT | Pet selection |
| 銀行框 | Bank | WINDOW_MESSAGETYPE_BANK | Bank interface |
| 寬對話框 | Wide Message | WINDOW_MESSAGETYPE_WIDEMESSAGE | Wide dialog |
| 拍賣框 | Auction | WINDOW_MESSAGETYPE_AUCTIONNEW | Auction interface |

### Button Types (按鈕類型)
| Chinese String | English Description | C Constant | Button Type |
|---------------|-------------------|-----------|-------------|
| 無按鈕 | None | WINDOW_BUTTONTYPE_NONE | No button |
| 確定 | OK | WINDOW_BUTTONTYPE_OK | OK button |
| 取消 | Cancel | WINDOW_BUTTONTYPE_CANCEL | Cancel button |
| YES | Yes | WINDOW_BUTTONTYPE_YES | Yes button |
| NO | No | WINDOW_BUTTONTYPE_NO | No button |
| 上一頁 | Previous | WINDOW_BUTTONTYPE_PREV | Previous page |
| 下一頁 | Next | WINDOW_BUTTONTYPE_NEXT | Next page |

---

## Pet and Enemy Attributes (寵物和敵人屬性)

### Pet/Enemy Template (寵物/敵人模板)
| Chinese String | English Description | C Constant | Property |
|---------------|-------------------|-----------|----------|
| 編號 | Template Number | E_T_TEMPNO | Template ID |
| 初始值 | Initial Number | E_T_INITNUM | Base stats |
| 成長率 | Level Up Point | E_T_LVUPPOINT | Growth rate |
| 體力 | Vitality | E_T_BASEVITAL | Base vitality |
| 腕力 | Strength | E_T_BASESTR | Base strength |
| 耐力 | Toughness | E_T_BASETGH | Base toughness |
| 速度 | Dexterity | E_T_BASEDEX | Base dexterity |
| 智能 | AI | E_T_MODAI | AI level |
| 給 | Get | E_T_GET | Unknown |
| 罕見 | Rare | E_T_RARE | Rarity |
| 暴擊率 | Critical Rate | E_T_CRITICAL | Critical chance |
| 反擊率 | Counter Rate | E_T_COUNTER | Counter chance |
| 技能數 | Skill Slots | E_T_SLOT | Number of skills |
| 形象 | Image Number | E_T_IMGNUMBER | Sprite image |
| 等級限制 | Level Limit | E_T_LIMITLEVEL | Level cap |
| 融合寵編碼 | Fusion Code | E_T_FUSIONCODE | Fusion data |
| 名字 | Name | E_T_NAME | Template name |

### Pet Skills (寵物技能)
| Chinese String | English Description | C Constant | Skill Slot |
|---------------|-------------------|-----------|------------|
| 技能1 | Skill 1 | E_T_PETSKILL1 | First skill |
| 技能2 | Skill 2 | E_T_PETSKILL2 | Second skill |
| 技能3 | Skill 3 | E_T_PETSKILL3 | Third skill |
| 技能4 | Skill 4 | E_T_PETSKILL4 | Fourth skill |
| 技能5 | Skill 5 | E_T_PETSKILL5 | Fifth skill |
| 技能6 | Skill 6 | E_T_PETSKILL6 | Sixth skill |
| 技能7 | Skill 7 | E_T_PETSKILL7 | Seventh skill |

---

## Usage Examples

### Basic Character Information Access
```lua
-- Get character name
local name = Char.getChar(charindex, "名字")
print("Character name: " .. name)

-- Set character level
Char.setInt(charindex, "等級", 50)

-- Get character HP
local hp = Char.getInt(charindex, "HP")
print("Character HP: " .. hp)

-- Set character position
Char.setInt(charindex, "地圖號", 1000)
Char.setInt(charindex, "坐標X", 100)
Char.setInt(charindex, "坐標Y", 100)
```

### Working with NPC Temporary Variables
```lua
-- Set NPC temporary variable 1
Char.setWorkInt(npcindex, "NPC臨時1", 100)

-- Get NPC temporary variable 2
local value = Char.getWorkInt(npcindex, "NPC臨時2")

-- Use character variables for strings
Char.setWorkChar(npcindex, "NPC臨時1", "Hello World")
local text = Char.getWorkChar(npcindex, "NPC臨時1")
```

### Status Effects Management
```lua
-- Check if character is poisoned
local poisoned = Char.getWorkInt(charindex, "毒")
if poisoned > 0 then
    print("Character is poisoned!")
end

-- Set sleep status
Char.setWorkInt(charindex, "睡", 5) -- Sleep for 5 rounds

-- Clear all status effects
Char.setWorkInt(charindex, "毒", 0)
Char.setWorkInt(charindex, "麻", 0)
Char.setWorkInt(charindex, "睡", 0)
```

### Item Information Access
```lua
-- Get item name
local itemname = Item.getChar(itemindex, "名稱")

-- Set item attack value
Item.setInt(itemindex, "攻", 50)

-- Check item type
local itemtype = Item.getInt(itemindex, "類型")
```

### Character Actions and Animation
```lua
-- Make character perform attack action
Char.setAction(charindex, "攻擊")

-- Make character sit
Char.setAction(charindex, "坐")

-- Make character show emotion
Char.setAction(charindex, "高興")

-- Make character turn
Char.setAction(charindex, "旋轉")
```

### Window and Dialog Management
```lua
-- Show a simple message window
NLG.ShowWindowTalked(playerindex, npcindex, "對話框", "NEXT", "Hello!", "確定", "取消")

-- Show input window
NLG.ShowWindowTalked(playerindex, npcindex, "輸入框", "NEXT", "Enter your name:", "確定", "取消")

-- Show selection window
NLG.ShowWindowTalked(playerindex, npcindex, "選擇框", "NEXT", "Choose option:", "選項1", "選項2")
```

### Color Usage in Messages
```lua
-- Send colored message to player
Char.TalkToCli(playerindex, -1, "This is red text!", "紅色")
Char.TalkToCli(playerindex, -1, "This is blue text!", "藍色")
Char.TalkToCli(playerindex, -1, "This is green text!", "綠色")
```

### Battle and Party Management
```lua
-- Check if character is in battle
local battlemode = Char.getWorkInt(charindex, "戰鬥")
if battlemode ~= 無戰鬥 then
    print("Character is in battle")
end

-- Check party status
local partymode = Char.getWorkInt(charindex, "組隊")
if partymode == "隊長" then
    print("Character is party leader")
elseif partymode == "隊員" then
    print("Character is party member")
end
```

---

## Important Notes

1. **Case Sensitivity**: All Chinese strings are case-sensitive and must match exactly.

2. **Encoding**: The source files use Big5 encoding for Chinese characters.

3. **String Categories**:
   - `Char.getInt()` / `Char.setInt()` - For integer values
   - `Char.getChar()` / `Char.setChar()` - For string values  
   - `Char.getWorkInt()` / `Char.setWorkInt()` - For work integer variables
   - `Char.getWorkChar()` / `Char.setWorkChar()` - For work string variables
   - `Char.getFlg()` / `Char.setFlg()` - For boolean flags

4. **Constants**: All C constants are defined in corresponding header files in the include/ directory.

5. **Error Handling**: Always check if character/item indices are valid before accessing attributes.

6. **Performance**: Avoid excessive polling of attributes in tight loops.

This reference covers the primary Lua interface strings from the mylua/ directory and provides a comprehensive mapping for Stone Age server scripting.