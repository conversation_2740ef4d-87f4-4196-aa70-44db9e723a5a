# mylua 字符串參考指南

本文檔提供 Stone Age mylua 腳本系統中所有中文字符屬性字符串、工作變數和其他重要字符串常數的完整映射。所有字符串都從 Big5 編碼解碼並映射到對應的 C 常數。

> **注意**: 本文檔是 mylua 系統的核心參考資料，包含所有可用的字符串常數和其對應的 C 定義。

## 目錄
1. [角色屬性](#角色屬性)
2. [工作變數](#工作變數)
3. [角色類型和模式](#角色類型和模式)
4. [顏色和視覺效果](#顏色和視覺效果)
5. [角色動作](#角色動作)
6. [角色標誌](#角色標誌)
7. [事件函數](#事件函數)
8. [道具屬性](#道具屬性)
9. [魔法屬性](#魔法屬性)
10. [窗口和UI類型](#窗口和ui類型)
11. [寵物和敵人屬性](#寵物和敵人屬性)
12. [使用範例](#使用範例)

---

## 角色屬性

### 基本角色信息
| 中文字符串 | 英文描述 | C 常數 | 類別 |
|-----------|---------|-------|------|
| 名字 | Name | CHAR_NAME | String |
| 暱稱 | Nickname/Title | CHAR_OWNTITLE | String |
| 賬號 | Account | CHAR_CDKEY | String |
| 主人 | Owner | CHAR_USERPETNAME | String |
| 主人賬號 | Owner Account | CHAR_OWNERCDKEY | String |
| 主人名字 | Owner Name | CHAR_OWNERCHARANAME | String |
| 稱號 | New Name/Title | CHAR_NEWNAME | String |
| 家族 | Family | CHAR_FMNAME | String |
| Unicode | Unicode | CHAR_UNIQUECODE | String |
| 精靈列表 | Magic Table | CHAR_MAGICTBL | String |

### 角色數值
| 中文字符串 | 英文描述 | C 常數 | 類別 |
|-----------|---------|-------|------|
| 等級 | Level | CHAR_LV | Integer |
| HP | HP | CHAR_HP | Integer |
| MP | MP | CHAR_MP | Integer |
| 最大MP | Max MP | CHAR_MAXMP | Integer |
| 體力 | Vitality | CHAR_VITAL | Integer |
| 腕力 | Strength | CHAR_STR | Integer |
| 耐力 | Toughness | CHAR_TOUGH | Integer |
| 速度 | Dexterity | CHAR_DEX | Integer |
| 魅力 | Charm | CHAR_CHARM | Integer |
| 運氣 | Luck | CHAR_LUCK | Integer |
| 石幣 | Gold | CHAR_GOLD | Integer |

### 位置信息
| 中文字符串 | 英文描述 | C 常數 | 類別 |
|-----------|---------|-------|------|
| 地圖號 | Floor | CHAR_FLOOR | Integer |
| 坐標X | X Coordinate | CHAR_X | Integer |
| 坐標Y | Y Coordinate | CHAR_Y | Integer |
| 方向 | Direction | CHAR_DIR | Integer |

### 家族相關
| 中文字符串 | 英文描述 | C 常數 | 類別 |
|-----------|---------|-------|------|
| 家族地位 | Family Leader Flag | CHAR_FMLEADERFLAG | Integer |
| 家族索引 | Family Index | CHAR_FMINDEX | Integer |
| 家族 | Family Name | CHAR_FMNAME | String |

**家族地位常數**:
- `FMMEMBER_NONE = -1` (沒有家族)
- `FMMEMBER_MEMBER = 1` (家族成員)
- `FMMEMBER_APPLY = 2` (申請入族)
- `FMMEMBER_LEADER = 3` (族長)
- `FMMEMBER_ELDER = 4` (長老)

## 工作變數

### NPC 工作變數
| 中文字符串 | 英文描述 | 用途 |
|-----------|---------|------|
| NPC變數1 | NPC Work Variable 1 | 通用整數變數 |
| NPC變數2 | NPC Work Variable 2 | 通用整數變數 |
| NPC變數3 | NPC Work Variable 3 | 通用整數變數 |
| NPC變數4 | NPC Work Variable 4 | 通用整數變數 |
| NPC變數5 | NPC Work Variable 5 | 通用整數變數 |
| NPC變數6 | NPC Work Variable 6 | 通用整數變數 |
| NPC變數7 | NPC Work Variable 7 | 通用整數變數 |
| NPC變數8 | NPC Work Variable 8 | 通用整數變數 |
| NPC變數9 | NPC Work Variable 9 | 通用整數變數 |
| NPC變數10 | NPC Work Variable 10 | 通用整數變數 |

### NPC 臨時變數
| 中文字符串 | 英文描述 | 用途 |
|-----------|---------|------|
| NPC臨時1 | NPC Temporary 1 | 臨時整數變數 |
| NPC臨時2 | NPC Temporary 2 | 臨時整數變數 |
| NPC臨時3 | NPC Temporary 3 | 臨時整數變數 |
| NPC臨時4 | NPC Temporary 4 | 臨時整數變數 |
| NPC臨時5 | NPC Temporary 5 | 臨時整數變數 |
| NPC臨時6 | NPC Temporary 6 | 臨時整數變數 |
| NPC臨時7 | NPC Temporary 7 | 臨時整數變數 |
| NPC臨時8 | NPC Temporary 8 | 臨時整數變數 |

### 角色工作變數
| 中文字符串 | 英文描述 | 用途 |
|-----------|---------|------|
| 對象 | Object Index | NPC 對象索引 |
| 組隊 | Party Mode | 組隊狀態 |
| 戰鬥 | Battle Mode | 戰鬥狀態 |

## 顏色和視覺效果

### 文字顏色
| 中文字符串 | 英文描述 | 顏色 |
|-----------|---------|------|
| 白色 | White | 預設顏色 |
| 紅色 | Red | 錯誤/警告 |
| 綠色 | Green | 成功/確認 |
| 藍色 | Blue | 信息 |
| 黃色 | Yellow | 注意 |
| 紫色 | Purple | 特殊 |
| 青色 | Cyan | 系統 |
| 黑色 | Black | 黑色文字 |

## 事件函數

### NPC 事件
| 中文字符串 | 英文描述 | 用途 |
|-----------|---------|------|
| 對話事件 | Talk Event | NPC 對話處理 |
| 窗口事件 | Window Event | 窗口對話處理 |
| 循環函數 | Loop Function | 定時循環處理 |
| 看板事件 | Watch Event | 看板事件處理 |
| 死亡事件 | Death Event | 死亡事件處理 |

## 使用範例

### 基本角色信息獲取
```lua
-- 獲取角色名稱
local name = char.getChar(charindex, "名字")
print("角色名稱: " .. name)

-- 設置角色等級
char.setInt(charindex, "等級", 50)

-- 獲取角色 HP
local hp = char.getInt(charindex, "HP")
print("角色 HP: " .. hp)

-- 設置角色位置
char.setInt(charindex, "地圖號", 1000)
char.setInt(charindex, "坐標X", 100)
char.setInt(charindex, "坐標Y", 100)
```

### 家族系統範例
```lua
-- 獲取家族信息
local family_status = char.getInt(talkerindex, "家族地位")
local family_index = char.getInt(talkerindex, "家族索引")
local family_name = char.getChar(talkerindex, "家族")

-- 檢查家族地位
if family_status == 3 then  -- FMMEMBER_LEADER
    print("這是族長")
elseif family_status == 1 then  -- FMMEMBER_MEMBER
    print("這是家族成員")
else
    print("沒有家族")
end
```

### NPC 工作變數使用
```lua
-- 設置 NPC 臨時變數 1
char.setWorkInt(npcindex, "NPC臨時1", 100)

-- 獲取 NPC 臨時變數 2
local value = char.getWorkInt(npcindex, "NPC臨時2")

-- 設置 NPC 對象索引
char.setWorkInt(npcindex, "對象", npcindex)
```

---

## 重要注意事項

1. **大小寫敏感**: 所有中文字符串都區分大小寫，必須完全匹配。

2. **編碼**: 源文件使用 Big5 編碼處理中文字符。

3. **字符串類別**:
   - `char.getInt()` / `char.setInt()` - 用於整數值
   - `char.getChar()` / `char.setChar()` - 用於字符串值  
   - `char.getWorkInt()` / `char.setWorkInt()` - 用於工作整數變數
   - `char.getWorkChar()` / `char.setWorkChar()` - 用於工作字符串變數
   - `char.getFlg()` / `char.setFlg()` - 用於布爾標誌

4. **常見錯誤**:
   - 使用 "姓名" 而不是 "名字"
   - 使用 "家族編號" 而不是 "家族索引"
   - 混淆工作變數和普通變數

5. **驗證方法**: 在使用任何字符串常數之前，建議檢查 mylua 源碼確認其存在性。

---

## 道具屬性

### 基本道具信息
| 中文字符串 | 英文描述 | C 常數 | 類別 |
|-----------|---------|-------|------|
| 序號 | ID | ITEM_ID | Integer |
| 名稱 | Name | ITEM_NAME | String |
| 顯示名 | Secret Name | ITEM_SECRETNAME | String |
| 圖號 | Image Number | ITEM_BASEIMAGENUMBER | Integer |
| 價值 | Cost | ITEM_COST | Integer |
| 售價 | Price | ITEM_PRICE | Integer |
| 類型 | Type | ITEM_TYPE | Integer |
| 等級 | Level | ITEM_LEVEL | Integer |
| 說明 | Description | ITEM_EFFECTSTRING | String |
| 字段 | Argument | ITEM_ARGUMENT | String |

### 道具屬性
| 中文字符串 | 英文描述 | C 常數 | 效果 |
|-----------|---------|-------|------|
| 攻 | Attack | ITEM_MODIFYATTACK | 攻擊力修正 |
| 防 | Defense | ITEM_MODIFYDEFENCE | 防禦力修正 |
| 敏 | Quick | ITEM_MODIFYQUICK | 速度修正 |
| HP | HP | ITEM_MODIFYHP | HP 修正 |
| MP | MP | ITEM_MODIFYMP | MP 修正 |
| 運氣 | Luck | ITEM_MODIFYLUCK | 運氣修正 |
| 魅力 | Charm | ITEM_MODIFYCHARM | 魅力修正 |
| 迴避 | Avoid | ITEM_MODIFYAVOID | 迴避修正 |
| 會心 | Critical | ITEM_CRITICAL | 會心率 |

### 道具需求
| 中文字符串 | 英文描述 | C 常數 | 需求 |
|-----------|---------|-------|------|
| 需攻 | Need Strength | ITEM_NEEDSTR | 力量需求 |
| 需敏 | Need Dexterity | ITEM_NEEDDEX | 敏捷需求 |
| 需轉 | Need Trans | ITEM_NEEDTRANS | 轉生需求 |
| 需職業 | Need Profession | ITEM_NEEDPROFESSION | 職業需求 |

### 道具類型
| 中文字符串 | 英文描述 | C 常數 | 裝備類型 |
|-----------|---------|-------|---------|
| 爪 | Fist | ITEM_FIST | 拳套武器 |
| 斧 | Axe | ITEM_AXE | 斧頭武器 |
| 棍 | Club | ITEM_CLUB | 棍棒武器 |
| 槍 | Spear | ITEM_SPEAR | 長槍武器 |
| 弓 | Bow | ITEM_BOW | 弓箭武器 |
| 盾 | Shield | ITEM_SHIELD | 盾牌 |
| 盔 | Helm | ITEM_HELM | 頭盔 |
| 鎧 | Armor | ITEM_ARMOUR | 盔甲 |
| 環 | Bracelet | ITEM_BRACELET | 手環 |
| 項 | Necklace | ITEM_NECKLACE | 項鍊 |
| 帶 | Belt | ITEM_BELT | 腰帶 |
| 耳環 | Earring | ITEM_EARRING | 耳環 |
| 鼻環 | Nose Ring | ITEM_NOSERING | 鼻環 |
| 護身符 | Amulet | ITEM_AMULET | 護身符 |
| 其它 | Other | ITEM_OTHER | 其他道具 |

### 道具事件
| 中文字符串 | 英文描述 | C 常數 | 事件類型 |
|-----------|---------|-------|---------|
| 使用事件 | Use Function | ITEM_USEFUNC | 使用時 |
| 裝上事件 | Attach Function | ITEM_ATTACHFUNC | 裝備時 |
| 卸下事件 | Detach Function | ITEM_DETACHFUNC | 卸下時 |
| 丟棄事件 | Drop Function | ITEM_DROPFUNC | 丟棄時 |
| 檢起事件 | Pickup Function | ITEM_PICKUPFUNC | 拾取時 |
| 死亡事件 | Die Relief Function | ITEM_DIERELIFEFUNC | 角色死亡時 |

## 魔法屬性

### 基本魔法信息
| 中文字符串 | 英文描述 | C 常數 | 類別 |
|-----------|---------|-------|------|
| ID | ID | MAGIC_ID | Integer |
| 名字 | Name | MAGIC_NAME | String |
| 註釋 | Comment | MAGIC_COMMENT | String |
| 函數名 | Function Name | MAGIC_FUNCNAME | String |
| 選項 | Option | MAGIC_OPTION | String |
| 字段 | Field | MAGIC_FIELD | Integer |
| 目標 | Target | MAGIC_TARGET | Integer |
| 死亡目標 | Dead Target | MAGIC_TARGET_DEADFLG | Integer |
| 攻擊精靈 | Attack Magic | MAGIC_IDX | Integer |

## 窗口和UI類型

### 窗口類型
| 中文字符串 | 英文描述 | C 常數 | 窗口類型 |
|-----------|---------|-------|---------|
| 對話框 | Message | WINDOW_MESSAGETYPE_MESSAGE | 對話框 |
| 輸入框 | Input | WINDOW_MESSAGETYPE_MESSAGEANDLINEINPUT | 輸入對話框 |
| 選擇框 | Select | WINDOW_MESSAGETYPE_SELECT | 選擇框 |
| 寵物框 | Pet Select | WINDOW_MESSAGETYPE_PETSELECT | 寵物選擇 |
| 銀行框 | Bank | WINDOW_MESSAGETYPE_BANK | 銀行界面 |
| 寬對話框 | Wide Message | WINDOW_MESSAGETYPE_WIDEMESSAGE | 寬對話框 |
| 拍賣框 | Auction | WINDOW_MESSAGETYPE_AUCTIONNEW | 拍賣界面 |

### 按鈕類型
| 中文字符串 | 英文描述 | C 常數 | 按鈕類型 |
|-----------|---------|-------|---------|
| 無按鈕 | None | WINDOW_BUTTONTYPE_NONE | 無按鈕 |
| 確定 | OK | WINDOW_BUTTONTYPE_OK | 確定按鈕 |
| 取消 | Cancel | WINDOW_BUTTONTYPE_CANCEL | 取消按鈕 |
| YES | Yes | WINDOW_BUTTONTYPE_YES | 是按鈕 |
| NO | No | WINDOW_BUTTONTYPE_NO | 否按鈕 |
| 上一頁 | Previous | WINDOW_BUTTONTYPE_PREV | 上一頁 |
| 下一頁 | Next | WINDOW_BUTTONTYPE_NEXT | 下一頁 |

## 寵物和敵人屬性

### 寵物/敵人模板
| 中文字符串 | 英文描述 | C 常數 | 屬性 |
|-----------|---------|-------|------|
| 編號 | Template Number | E_T_TEMPNO | 模板ID |
| 初始值 | Initial Number | E_T_INITNUM | 基礎數值 |
| 成長率 | Level Up Point | E_T_LVUPPOINT | 成長率 |
| 體力 | Vitality | E_T_BASEVITAL | 基礎體力 |
| 腕力 | Strength | E_T_BASESTR | 基礎力量 |
| 耐力 | Toughness | E_T_BASETGH | 基礎耐力 |
| 速度 | Dexterity | E_T_BASEDEX | 基礎敏捷 |
| 智能 | AI | E_T_MODAI | AI等級 |
| 給 | Get | E_T_GET | 未知 |
| 罕見 | Rare | E_T_RARE | 稀有度 |
| 暴擊率 | Critical Rate | E_T_CRITICAL | 暴擊機率 |
| 反擊率 | Counter Rate | E_T_COUNTER | 反擊機率 |
| 技能數 | Skill Slots | E_T_SLOT | 技能數量 |
| 形象 | Image Number | E_T_IMGNUMBER | 精靈圖像 |
| 等級限制 | Level Limit | E_T_LIMITLEVEL | 等級上限 |
| 融合寵編碼 | Fusion Code | E_T_FUSIONCODE | 融合數據 |
| 名字 | Name | E_T_NAME | 模板名稱 |

### 寵物技能
| 中文字符串 | 英文描述 | C 常數 | 技能欄位 |
|-----------|---------|-------|---------|
| 技能1 | Skill 1 | E_T_PETSKILL1 | 第一技能 |
| 技能2 | Skill 2 | E_T_PETSKILL2 | 第二技能 |
| 技能3 | Skill 3 | E_T_PETSKILL3 | 第三技能 |
| 技能4 | Skill 4 | E_T_PETSKILL4 | 第四技能 |
| 技能5 | Skill 5 | E_T_PETSKILL5 | 第五技能 |
| 技能6 | Skill 6 | E_T_PETSKILL6 | 第六技能 |
| 技能7 | Skill 7 | E_T_PETSKILL7 | 第七技能 |
