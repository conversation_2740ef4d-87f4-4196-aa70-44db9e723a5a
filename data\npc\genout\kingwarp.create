NPCCREATE
# generated by npcgen.perl which is made by ringo
#
#
# infile : data/npc/king/kingwarp.gen
#line 2
{
floorid=300
borncorner=266,284,266,284
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30606|0|6
}
# between
{
floorid=30606
borncorner=0,6,0,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|300|266|284
}
#line 3
{
floorid=30601
borncorner=86,34,86,34
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30606|28|9
}
#line 4
{
floorid=30604
borncorner=20,53,20,53
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30605|7|24
}
# between
{
floorid=30605
borncorner=7,24,7,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30604|20|53
}
#line 5
{
floorid=30607
borncorner=7,24,7,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30604|20|53
}
#line 8
{
floorid=30611
borncorner=7,23,7,23
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30611|8|4
}
# between
{
floorid=30611
borncorner=8,4,8,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30611|7|23
}
#line 9
{
floorid=30611
borncorner=44,4,44,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30612|25|25
}
# between
{
floorid=30612
borncorner=25,25,25,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30611|44|4
}
#line 10
{
floorid=30612
borncorner=16,25,16,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30612|7|13
}
# between
{
floorid=30612
borncorner=7,13,7,13
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30612|16|25
}
#line 11
{
floorid=30612
borncorner=40,14,40,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30613|24|23
}
# between
{
floorid=30613
borncorner=24,23,24,23
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30612|40|14
}
#line 12
{
floorid=30613
borncorner=26,29,26,29
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30613|34|8
}
# between
{
floorid=30613
borncorner=34,8,34,8
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30613|26|29
}
#line 13
{
floorid=30613
borncorner=35,41,35,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30614|28|25
}
# between
{
floorid=30614
borncorner=28,25,28,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30613|35|41
}
#line 14
{
floorid=30614
borncorner=43,20,43,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30614|7|42
}
# between
{
floorid=30614
borncorner=7,42,7,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30614|43|20
}
#line 15
{
floorid=30614
borncorner=43,40,43,40
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|25|22
}
# between
{
floorid=30615
borncorner=25,22,25,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30614|43|40
}
#line 17
{
floorid=30611
borncorner=16,22,16,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30611|10|14
}
# between
{
floorid=30611
borncorner=10,14,10,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30611|16|22
}
#line 18
{
floorid=30611
borncorner=43,13,43,13
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30612|6|32
}
# between
{
floorid=30612
borncorner=6,32,6,32
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30611|43|13
}
#line 19
{
floorid=30612
borncorner=39,33,39,33
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30613|45|5
}
# between
{
floorid=30613
borncorner=45,5,45,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30612|39|33
}
#line 20
{
floorid=30613
borncorner=45,41,45,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30614|8|3
}
# between
{
floorid=30614
borncorner=8,3,8,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30613|45|41
}
#line 21
{
floorid=30614
borncorner=41,3,41,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30611|25|22
}
#line 23
{
floorid=30611
borncorner=34,22,34,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30611|7|32
}
# between
{
floorid=30611
borncorner=7,32,7,32
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30611|34|22
}
#line 24
{
floorid=30611
borncorner=43,31,43,31
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30612|7|41
}
# between
{
floorid=30612
borncorner=7,41,7,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30611|43|31
}
#line 25
{
floorid=30612
borncorner=43,43,43,43
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30613|6|8
}
# between
{
floorid=30613
borncorner=6,8,6,8
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30612|43|43
}
#line 26
{
floorid=30613
borncorner=6,44,6,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30614|10|11
}
# between
{
floorid=30614
borncorner=10,11,10,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30613|6|44
}
#line 27
{
floorid=30614
borncorner=43,10,43,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30611|25|22
}
#line 29
{
floorid=30611
borncorner=43,22,43,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30611|7|42
}
# between
{
floorid=30611
borncorner=7,42,7,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30611|43|22
}
#line 30
{
floorid=30611
borncorner=40,41,40,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30612|8|4
}
# between
{
floorid=30612
borncorner=8,4,8,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30611|40|41
}
#line 31
{
floorid=30612
borncorner=44,4,44,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30613|16|5
}
# between
{
floorid=30613
borncorner=16,5,16,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30612|44|4
}
#line 32
{
floorid=30613
borncorner=16,41,16,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30614|7|32
}
# between
{
floorid=30614
borncorner=7,32,7,32
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30613|16|41
}
#line 33
{
floorid=30614
borncorner=43,30,43,30
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30611|25|22
}
#line 35
{
floorid=30612
borncorner=7,22,7,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30613|16|5
}
#line 36
{
floorid=30613
borncorner=16,41,16,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30614|7|32
}
# between
{
floorid=30614
borncorner=7,32,7,32
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30613|16|41
}
#line 37
{
floorid=30614
borncorner=43,30,43,30
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30611|25|22
}
#line 39
{
floorid=30612
borncorner=34,22,34,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30613|45|5
}
#line 40
{
floorid=30613
borncorner=45,41,45,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30614|8|3
}
# between
{
floorid=30614
borncorner=8,3,8,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30613|45|41
}
#line 41
{
floorid=30614
borncorner=41,3,41,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30611|25|22
}
#line 43
{
floorid=30612
borncorner=43,24,43,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30613|6|8
}
#line 44
{
floorid=30613
borncorner=6,44,6,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30614|10|11
}
# between
{
floorid=30614
borncorner=10,11,10,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30613|6|44
}
#line 45
{
floorid=30614
borncorner=43,10,43,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30611|25|22
}
#line 47
{
floorid=30613
borncorner=26,5,26,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30614|10|11
}
#line 48
{
floorid=30614
borncorner=43,10,43,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30611|25|22
}
#line 50
{
floorid=30613
borncorner=23,14,23,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30614|7|32
}
#line 51
{
floorid=30614
borncorner=43,30,43,30
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30611|25|22
}
#line 53
{
floorid=30613
borncorner=25,44,25,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30614|8|3
}
#line 54
{
floorid=30614
borncorner=41,3,41,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30611|25|22
}
#line 56
{
floorid=30614
borncorner=7,23,7,23
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30614|10|11
}
#line 57
{
floorid=30614
borncorner=19,22,19,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30614|7|32
}
#line 58
{
floorid=30614
borncorner=37,22,37,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30614|8|3
}
#line 60
{
floorid=30618
borncorner=7,23,7,23
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30618|10|11
}
#line 61
{
floorid=30618
borncorner=19,22,19,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30618|7|32
}
#line 62
{
floorid=30618
borncorner=37,22,37,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30618|8|3
}
#line 64
{
floorid=30624
borncorner=7,23,7,23
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30624|10|11
}
#line 65
{
floorid=30624
borncorner=19,22,19,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30624|7|32
}
#line 66
{
floorid=30624
borncorner=37,22,37,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30624|8|3
}
#line 68
{
floorid=30628
borncorner=7,23,7,23
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30628|10|11
}
#line 69
{
floorid=30628
borncorner=19,22,19,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30628|7|32
}
#line 70
{
floorid=30628
borncorner=37,22,37,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30628|8|3
}
#line 72
{
floorid=30615
borncorner=7,23,7,23
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|8|4
}
# between
{
floorid=30615
borncorner=8,4,8,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|7|23
}
#line 73
{
floorid=30615
borncorner=44,4,44,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30616|25|25
}
# between
{
floorid=30616
borncorner=25,25,25,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|44|4
}
#line 74
{
floorid=30616
borncorner=16,25,16,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30616|7|13
}
# between
{
floorid=30616
borncorner=7,13,7,13
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30616|16|25
}
#line 75
{
floorid=30616
borncorner=40,14,40,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30617|24|23
}
# between
{
floorid=30617
borncorner=24,23,24,23
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30616|40|14
}
#line 76
{
floorid=30617
borncorner=26,29,26,29
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30617|34|8
}
# between
{
floorid=30617
borncorner=34,8,34,8
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30617|26|29
}
#line 77
{
floorid=30617
borncorner=35,41,35,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30618|28|25
}
# between
{
floorid=30618
borncorner=28,25,28,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30617|35|41
}
#line 78
{
floorid=30618
borncorner=43,20,43,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30618|7|42
}
# between
{
floorid=30618
borncorner=7,42,7,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30618|43|20
}
#line 79
{
floorid=30618
borncorner=43,40,43,40
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30619|34|20
}
# between
{
floorid=30619
borncorner=34,20,34,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30618|43|40
}
#line 81
{
floorid=30615
borncorner=16,22,16,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|10|14
}
# between
{
floorid=30615
borncorner=10,14,10,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|16|22
}
#line 82
{
floorid=30615
borncorner=43,13,43,13
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30616|6|32
}
# between
{
floorid=30616
borncorner=6,32,6,32
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|43|13
}
#line 83
{
floorid=30616
borncorner=39,33,39,33
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30617|45|5
}
# between
{
floorid=30617
borncorner=45,5,45,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30616|39|33
}
#line 84
{
floorid=30617
borncorner=45,41,45,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30618|8|3
}
# between
{
floorid=30618
borncorner=8,3,8,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30617|45|41
}
#line 85
{
floorid=30618
borncorner=41,3,41,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|25|22
}
#line 87
{
floorid=30615
borncorner=34,22,34,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|7|32
}
# between
{
floorid=30615
borncorner=7,32,7,32
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|34|22
}
#line 88
{
floorid=30615
borncorner=43,31,43,31
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30616|7|41
}
# between
{
floorid=30616
borncorner=7,41,7,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|43|31
}
#line 89
{
floorid=30616
borncorner=43,43,43,43
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30617|6|8
}
# between
{
floorid=30617
borncorner=6,8,6,8
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30616|43|43
}
#line 90
{
floorid=30617
borncorner=6,44,6,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30618|10|11
}
# between
{
floorid=30618
borncorner=10,11,10,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30617|6|44
}
#line 91
{
floorid=30618
borncorner=43,10,43,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|25|22
}
#line 93
{
floorid=30615
borncorner=43,22,43,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|7|42
}
# between
{
floorid=30615
borncorner=7,42,7,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|43|22
}
#line 94
{
floorid=30615
borncorner=40,41,40,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30616|8|4
}
# between
{
floorid=30616
borncorner=8,4,8,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|40|41
}
#line 95
{
floorid=30616
borncorner=44,4,44,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30617|16|5
}
# between
{
floorid=30617
borncorner=16,5,16,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30616|44|4
}
#line 96
{
floorid=30617
borncorner=16,41,16,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30618|7|32
}
# between
{
floorid=30618
borncorner=7,32,7,32
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30617|16|41
}
#line 97
{
floorid=30618
borncorner=43,30,43,30
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|25|22
}
#line 99
{
floorid=30616
borncorner=7,22,7,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30617|16|5
}
#line 100
{
floorid=30617
borncorner=16,41,16,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30618|7|32
}
# between
{
floorid=30618
borncorner=7,32,7,32
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30617|16|41
}
#line 101
{
floorid=30618
borncorner=43,30,43,30
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|25|22
}
#line 103
{
floorid=30616
borncorner=34,22,34,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30617|45|5
}
#line 104
{
floorid=30617
borncorner=45,41,45,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30618|8|3
}
# between
{
floorid=30618
borncorner=8,3,8,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30617|45|41
}
#line 105
{
floorid=30618
borncorner=41,3,41,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|25|22
}
#line 107
{
floorid=30616
borncorner=43,24,43,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30617|6|8
}
#line 108
{
floorid=30617
borncorner=6,44,6,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30618|10|11
}
# between
{
floorid=30618
borncorner=10,11,10,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30617|6|44
}
#line 109
{
floorid=30618
borncorner=43,10,43,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|25|22
}
#line 111
{
floorid=30617
borncorner=26,5,26,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30618|10|11
}
#line 112
{
floorid=30618
borncorner=43,10,43,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|25|22
}
#line 114
{
floorid=30617
borncorner=23,14,23,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30618|7|32
}
#line 115
{
floorid=30618
borncorner=43,30,43,30
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|25|22
}
#line 117
{
floorid=30617
borncorner=25,44,25,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30618|8|3
}
#line 118
{
floorid=30618
borncorner=41,3,41,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30615|25|22
}
#line 120
{
floorid=30621
borncorner=7,23,7,23
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30621|8|4
}
# between
{
floorid=30621
borncorner=8,4,8,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30621|7|23
}
#line 121
{
floorid=30621
borncorner=44,4,44,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30622|25|25
}
# between
{
floorid=30622
borncorner=25,25,25,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30621|44|4
}
#line 122
{
floorid=30622
borncorner=16,25,16,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30622|7|13
}
# between
{
floorid=30622
borncorner=7,13,7,13
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30622|16|25
}
#line 123
{
floorid=30622
borncorner=40,14,40,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30623|24|23
}
# between
{
floorid=30623
borncorner=24,23,24,23
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30622|40|14
}
#line 124
{
floorid=30623
borncorner=26,29,26,29
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30623|34|8
}
# between
{
floorid=30623
borncorner=34,8,34,8
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30623|26|29
}
#line 125
{
floorid=30623
borncorner=35,41,35,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30624|28|25
}
# between
{
floorid=30624
borncorner=28,25,28,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30623|35|41
}
#line 126
{
floorid=30624
borncorner=43,20,43,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30624|7|42
}
# between
{
floorid=30624
borncorner=7,42,7,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30624|43|20
}
#line 127
{
floorid=30624
borncorner=43,40,43,40
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|25|22
}
# between
{
floorid=30625
borncorner=25,22,25,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30624|43|40
}
#line 129
{
floorid=30621
borncorner=16,22,16,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30621|10|14
}
# between
{
floorid=30621
borncorner=10,14,10,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30621|16|22
}
#line 130
{
floorid=30621
borncorner=43,13,43,13
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30622|6|32
}
# between
{
floorid=30622
borncorner=6,32,6,32
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30621|43|13
}
#line 131
{
floorid=30622
borncorner=39,33,39,33
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30623|45|5
}
# between
{
floorid=30623
borncorner=45,5,45,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30622|39|33
}
#line 132
{
floorid=30623
borncorner=45,41,45,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30624|8|3
}
# between
{
floorid=30624
borncorner=8,3,8,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30623|45|41
}
#line 133
{
floorid=30624
borncorner=41,3,41,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30621|25|22
}
#line 135
{
floorid=30621
borncorner=34,22,34,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30621|7|32
}
# between
{
floorid=30621
borncorner=7,32,7,32
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30621|34|22
}
#line 136
{
floorid=30621
borncorner=43,31,43,31
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30622|7|41
}
# between
{
floorid=30622
borncorner=7,41,7,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30621|43|31
}
#line 137
{
floorid=30622
borncorner=43,43,43,43
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30623|6|8
}
# between
{
floorid=30623
borncorner=6,8,6,8
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30622|43|43
}
#line 138
{
floorid=30623
borncorner=6,44,6,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30624|10|11
}
# between
{
floorid=30624
borncorner=10,11,10,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30623|6|44
}
#line 139
{
floorid=30624
borncorner=43,10,43,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30621|25|22
}
#line 141
{
floorid=30621
borncorner=43,22,43,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30621|7|42
}
# between
{
floorid=30621
borncorner=7,42,7,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30621|43|22
}
#line 142
{
floorid=30621
borncorner=40,41,40,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30622|8|4
}
# between
{
floorid=30622
borncorner=8,4,8,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30621|40|41
}
#line 143
{
floorid=30622
borncorner=44,4,44,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30623|16|5
}
# between
{
floorid=30623
borncorner=16,5,16,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30622|44|4
}
#line 144
{
floorid=30623
borncorner=16,41,16,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30624|7|32
}
# between
{
floorid=30624
borncorner=7,32,7,32
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30623|16|41
}
#line 145
{
floorid=30624
borncorner=43,30,43,30
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30621|25|22
}
#line 147
{
floorid=30622
borncorner=7,22,7,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30623|16|5
}
#line 148
{
floorid=30623
borncorner=16,41,16,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30624|7|32
}
# between
{
floorid=30624
borncorner=7,32,7,32
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30623|16|41
}
#line 149
{
floorid=30624
borncorner=43,30,43,30
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30621|25|22
}
#line 151
{
floorid=30622
borncorner=34,22,34,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30623|45|5
}
#line 152
{
floorid=30623
borncorner=45,41,45,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30624|8|3
}
# between
{
floorid=30624
borncorner=8,3,8,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30623|45|41
}
#line 153
{
floorid=30624
borncorner=41,3,41,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30621|25|22
}
#line 155
{
floorid=30622
borncorner=43,24,43,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30623|6|8
}
#line 156
{
floorid=30623
borncorner=6,44,6,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30624|10|11
}
# between
{
floorid=30624
borncorner=10,11,10,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30623|6|44
}
#line 157
{
floorid=30624
borncorner=43,10,43,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30621|25|22
}
#line 159
{
floorid=30623
borncorner=26,5,26,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30624|10|11
}
#line 160
{
floorid=30624
borncorner=43,10,43,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30621|25|22
}
#line 162
{
floorid=30623
borncorner=23,14,23,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30624|7|32
}
#line 163
{
floorid=30624
borncorner=43,30,43,30
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30621|25|22
}
#line 165
{
floorid=30623
borncorner=25,44,25,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30624|8|3
}
#line 166
{
floorid=30624
borncorner=41,3,41,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30621|25|22
}
#line 168
{
floorid=30625
borncorner=7,23,7,23
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|8|4
}
# between
{
floorid=30625
borncorner=8,4,8,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|7|23
}
#line 169
{
floorid=30625
borncorner=44,4,44,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30626|25|25
}
# between
{
floorid=30626
borncorner=25,25,25,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|44|4
}
#line 170
{
floorid=30626
borncorner=16,25,16,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30626|7|13
}
# between
{
floorid=30626
borncorner=7,13,7,13
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30626|16|25
}
#line 171
{
floorid=30626
borncorner=40,14,40,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30627|24|23
}
# between
{
floorid=30627
borncorner=24,23,24,23
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30626|40|14
}
#line 172
{
floorid=30627
borncorner=26,29,26,29
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30627|34|8
}
# between
{
floorid=30627
borncorner=34,8,34,8
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30627|26|29
}
#line 173
{
floorid=30627
borncorner=35,41,35,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30628|28|25
}
# between
{
floorid=30628
borncorner=28,25,28,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30627|35|41
}
#line 174
{
floorid=30628
borncorner=43,20,43,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30628|7|42
}
# between
{
floorid=30628
borncorner=7,42,7,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30628|43|20
}
#line 175
{
floorid=30628
borncorner=43,40,43,40
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30620|34|20
}
# between
{
floorid=30620
borncorner=34,20,34,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30628|43|40
}
#line 177
{
floorid=30625
borncorner=16,22,16,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|10|14
}
# between
{
floorid=30625
borncorner=10,14,10,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|16|22
}
#line 178
{
floorid=30625
borncorner=43,13,43,13
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30626|6|32
}
# between
{
floorid=30626
borncorner=6,32,6,32
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|43|13
}
#line 179
{
floorid=30626
borncorner=39,33,39,33
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30627|45|5
}
# between
{
floorid=30627
borncorner=45,5,45,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30626|39|33
}
#line 180
{
floorid=30627
borncorner=45,41,45,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30628|8|3
}
# between
{
floorid=30628
borncorner=8,3,8,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30627|45|41
}
#line 181
{
floorid=30628
borncorner=41,3,41,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|25|22
}
#line 183
{
floorid=30625
borncorner=34,22,34,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|7|32
}
# between
{
floorid=30625
borncorner=7,32,7,32
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|34|22
}
#line 184
{
floorid=30625
borncorner=43,31,43,31
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30626|7|41
}
# between
{
floorid=30626
borncorner=7,41,7,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|43|31
}
#line 185
{
floorid=30626
borncorner=43,43,43,43
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30627|6|8
}
# between
{
floorid=30627
borncorner=6,8,6,8
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30626|43|43
}
#line 186
{
floorid=30627
borncorner=6,44,6,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30628|10|11
}
# between
{
floorid=30628
borncorner=10,11,10,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30627|6|44
}
#line 187
{
floorid=30628
borncorner=43,10,43,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|25|22
}
#line 189
{
floorid=30625
borncorner=43,22,43,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|7|42
}
# between
{
floorid=30625
borncorner=7,42,7,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|43|22
}
#line 190
{
floorid=30625
borncorner=40,41,40,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30626|8|4
}
# between
{
floorid=30626
borncorner=8,4,8,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|40|41
}
#line 191
{
floorid=30626
borncorner=44,4,44,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30627|16|5
}
# between
{
floorid=30627
borncorner=16,5,16,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30626|44|4
}
#line 192
{
floorid=30627
borncorner=16,41,16,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30628|7|32
}
# between
{
floorid=30628
borncorner=7,32,7,32
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30627|16|41
}
#line 193
{
floorid=30628
borncorner=43,30,43,30
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|25|22
}
#line 195
{
floorid=30626
borncorner=7,22,7,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30627|16|5
}
#line 196
{
floorid=30627
borncorner=16,41,16,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30628|7|32
}
# between
{
floorid=30628
borncorner=7,32,7,32
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30627|16|41
}
#line 197
{
floorid=30628
borncorner=43,30,43,30
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|25|22
}
#line 199
{
floorid=30626
borncorner=34,22,34,22
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30627|45|5
}
#line 200
{
floorid=30627
borncorner=45,41,45,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30628|8|3
}
# between
{
floorid=30628
borncorner=8,3,8,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30627|45|41
}
#line 201
{
floorid=30628
borncorner=41,3,41,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|25|22
}
#line 203
{
floorid=30626
borncorner=43,24,43,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30627|6|8
}
#line 204
{
floorid=30627
borncorner=6,44,6,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30628|10|11
}
# between
{
floorid=30628
borncorner=10,11,10,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30627|6|44
}
#line 205
{
floorid=30628
borncorner=43,10,43,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|25|22
}
#line 207
{
floorid=30627
borncorner=26,5,26,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30628|10|11
}
#line 208
{
floorid=30628
borncorner=43,10,43,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|25|22
}
#line 210
{
floorid=30627
borncorner=23,14,23,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30628|7|32
}
#line 211
{
floorid=30628
borncorner=43,30,43,30
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|25|22
}
#line 213
{
floorid=30627
borncorner=25,44,25,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30628|8|3
}
#line 214
{
floorid=30628
borncorner=41,3,41,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30625|25|22
}
#line 217
{
floorid=30696
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30691|47|71
}
#line 218
{
floorid=30696
borncorner=23,14,23,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30692|34|20
}
# between
{
floorid=30692
borncorner=34,20,34,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30696|23|14
}
#line 219
{
floorid=30697
borncorner=0,14,0,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30692|57|67
}
#line 220
{
floorid=30697
borncorner=25,14,25,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30693|61|17
}
# between
{
floorid=30693
borncorner=61,17,61,17
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30697|25|14
}
#line 221
{
floorid=30698
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30693|58|36
}
#line 222
{
floorid=30698
borncorner=23,6,23,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30694|72|9
}
# between
{
floorid=30694
borncorner=72,9,72,9
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30698|23|6
}
#line 223
{
floorid=30694
borncorner=69,56,69,56
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30694|38|49
}
# between
{
floorid=30694
borncorner=38,49,38,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30694|69|56
}
#line 224
{
floorid=30699
borncorner=0,12,0,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30694|52|37
}
#line 225
{
floorid=30699
borncorner=24,6,24,6
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30695|28|44
}
# between
{
floorid=30695
borncorner=28,44,28,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30699|24|6
}
#line 226
{
floorid=30670
borncorner=15,29,15,29
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30695|41|27
}
#line 230
{
floorid=30689
borncorner=19,34,19,34
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|28|11
}
# between
{
floorid=30689
borncorner=28,11,28,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|19|34
}
#line 231
{
floorid=30689
borncorner=33,10,33,10
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|35|26
}
# between
{
floorid=30689
borncorner=35,26,35,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|33|10
}
#line 232
{
floorid=30689
borncorner=35,24,35,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|23|51
}
# between
{
floorid=30689
borncorner=23,51,23,51
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|35|24
}
#line 233
{
floorid=30689
borncorner=29,42,29,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|43|49
}
# between
{
floorid=30689
borncorner=43,49,43,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|29|42
}
#line 234
{
floorid=30689
borncorner=50,42,50,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|19|38
}
# between
{
floorid=30689
borncorner=19,38,19,38
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|50|42
}
#line 235
{
floorid=30689
borncorner=12,46,12,46
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|37|19
}
# between
{
floorid=30689
borncorner=37,19,37,19
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|12|46
}
#line 236
{
floorid=30689
borncorner=44,19,44,19
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|42|29
}
#line 238
{
floorid=30689
borncorner=19,46,19,46
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|24|36
}
# between
{
floorid=30689
borncorner=24,36,24,36
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|19|46
}
#line 239
{
floorid=30689
borncorner=24,26,24,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|9|47
}
# between
{
floorid=30689
borncorner=9,47,9,47
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|24|26
}
#line 240
{
floorid=30689
borncorner=12,51,12,51
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|33|51
}
# between
{
floorid=30689
borncorner=33,51,33,51
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|12|51
}
#line 241
{
floorid=30689
borncorner=38,42,38,42
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|20|11
}
# between
{
floorid=30689
borncorner=20,11,20,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|38|42
}
#line 242
{
floorid=30689
borncorner=12,19,12,19
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|29|27
}
# between
{
floorid=30689
borncorner=29,27,29,27
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|12|19
}
#line 243
{
floorid=30689
borncorner=32,35,32,35
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|31|23
}
# between
{
floorid=30689
borncorner=31,23,31,23
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30689|32|35
}
#line 245
{
floorid=30689
borncorner=47,9,47,9
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30691|84|63
}
#line 246
{
floorid=30689
borncorner=24,23,24,23
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30691|84|63
}
#line 247
{
floorid=30689
borncorner=49,50,49,50
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30691|84|63
}
#line 248
{
floorid=30689
borncorner=16,24,16,24
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30691|84|63
}
#line 250
{
floorid=30689
borncorner=39,51,39,51
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30692|23|35
}
#line 251
{
floorid=30689
borncorner=32,28,32,28
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30692|23|35
}
#line 252
{
floorid=30689
borncorner=38,9,38,9
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|30692|23|35
}
