# mylua 系統參考文檔

這個資料夾包含 Stone Age 服務器 mylua 系統的完整技術參考，適用於所有 Lua 項目開發。

## 📁 文檔結構

```
mylua_reference/
├── README.md                    # 本文檔 (mylua 參考索引)
├── coding_reference.md          # 編程參考手冊
├── best_practices.md            # 開發最佳實踐
├── common_errors.md             # 常見錯誤避免
├── function_usage.md            # 函數使用指南
└── testing_and_debugging.md     # 測試和調試指南
```

## 📚 文檔說明

### 🔍 [編程參考手冊](./coding_reference.md)
mylua 系統的核心技術參考：
- **函數調用規範**: NPC 創建、窗口系統、跨文件調用
- **變數名稱對照**: 家族系統、道具操作、玩家屬性
- **參數類型說明**: 函數參數格式和返回值
- **源碼檢查方法**: 如何驗證函數和變數的正確用法

### 🏆 [開發最佳實踐](./best_practices.md)
經過驗證的開發習慣和流程：
- **源碼檢查流程**: 每次使用前的檢查步驟
- **編碼規範**: Big5 編碼、文件組織、命名規則
- **權限控制設計**: 分層權限、錯誤處理、用戶體驗
- **文檔維護**: 記錄檢查結果、更新指南

### ⚠️ [常見錯誤避免](./common_errors.md)
開發中容易犯的錯誤和解決方案：
- **變數名稱錯誤**: 使用不存在的變數名
- **函數參數錯誤**: 參數順序或格式錯誤
- **編碼問題**: UTF-8 導致的中文亂碼
- **NPC 創建問題**: 事件處理但沒有 NPC
- **窗口函數錯誤**: 錯誤的對象索引參數

### 🔧 [函數使用指南](./function_usage.md)
具體的函數使用範例和模式：
- **NPC 創建模式**: 標準的 NPC 創建和設置流程
- **窗口系統使用**: 倉庫、對話、選單窗口的實現
- **數據庫操作**: 查詢、插入、更新的最佳實踐
- **跨文件調用**: other.CallFunction 的正確使用方式

### 🧪 [測試和調試指南](./testing_and_debugging.md)
mylua 系統的測試和調試策略：
- **靜態代碼分析**: 語法檢查和代碼驗證
- **模擬測試環境**: 創建 mylua 函數模擬器
- **單元測試框架**: 功能測試和集成測試
- **服務器端調試**: 日誌記錄和錯誤處理

## 🎯 使用指南

### 🔰 新手開發者學習路徑
1. **第一步**: 閱讀 [編程參考手冊](./coding_reference.md) 了解基礎
2. **第二步**: 學習 [最佳實踐](./best_practices.md) 建立良好習慣
3. **第三步**: 參考 [函數使用指南](./function_usage.md) 進行實際開發
4. **第四步**: 查看 [常見錯誤](./common_errors.md) 避免常見問題

### 👨‍💻 經驗開發者快速查詢
1. **函數查詢**: 使用 [編程參考手冊](./coding_reference.md) 作為速查手冊
2. **新模式學習**: 查看 [函數使用指南](./function_usage.md) 了解新的實現模式
3. **問題解決**: 參考 [常見錯誤](./common_errors.md) 快速定位問題

### 🤖 AI 助手使用指南
1. **函數驗證**: 使用 [編程參考手冊](./coding_reference.md) 確認函數正確用法
2. **流程遵循**: 按照 [最佳實踐](./best_practices.md) 中的檢查流程操作
3. **範例提供**: 參考 [函數使用指南](./function_usage.md) 提供準確的代碼範例
4. **錯誤避免**: 查看 [常見錯誤](./common_errors.md) 避免重複錯誤

## 🔍 核心檢查原則

### 1. 源碼檢查優先
```bash
# 每次使用 mylua 函數前都要檢查源碼
grep -r "function_name" mylua/
grep -r "變數名" mylua/charbase.c
grep -r "CONSTANT_NAME" include/
```

### 2. 參考現有實現
```lua
-- 學習 pool.lua, donate.lua 等現有系統腳本
-- 遵循既定的實現模式和編碼規範
```

### 3. 記錄檢查結果
```lua
-- ✅ 已檢查 mylua/lssprotobase.c 確認正確用法
lssproto.windows(talkerindex, 1103, 8, 0, char.getWorkInt(npcindex, "對象"), data)
```

### 4. 使用正確編碼
```lua
-- 使用 Big5 編碼確保中文顯示正常
-- 文件放置在 data/ablua/npc/system/ 目錄
```

## 📋 開發檢查清單

### 使用新函數前
- [ ] 檢查 mylua/*.c 源碼確認函數存在
- [ ] 確認參數類型、順序和返回值
- [ ] 查看現有腳本的使用範例
- [ ] 在代碼中註釋檢查結果

### 使用新變數前
- [ ] 檢查 mylua/charbase.c 確認變數名稱
- [ ] 查看 include/*.h 確認常數定義
- [ ] 測試變數是否返回預期值
- [ ] 記錄正確的變數名稱

### 創建新腳本前
- [ ] 確定文件放置位置 (data/ablua/npc/system/)
- [ ] 使用 Big5 編碼
- [ ] 參考現有腳本的結構
- [ ] 設計適當的權限控制

## 🛠️ 相關資源

### 源碼位置
- `mylua/*.c` - C 函數實現
- `include/*.h` - 常數和枚舉定義
- `data/ablua/npc/system/*.lua` - 現有系統腳本

### 重要參考腳本
- `pool.lua` - 倉庫系統，學習 NPC 創建和窗口處理
- `donate.lua` - 捐獻系統，學習道具操作
- `fmreward.lua` - 家族獎勵，學習家族變數使用

### 常用檢查命令
```bash
# 檢查函數定義
grep -r "function_name" mylua/

# 檢查變數名稱
grep -r "變數名" mylua/charbase.c

# 檢查枚舉常數
grep -r "ENUM_NAME" include/

# 查看現有腳本用法
grep -r "function_name" data/ablua/npc/system/
```

## 🎯 文檔維護

### 更新原則
- 每次發現新的函數用法時更新編程參考
- 遇到新的錯誤模式時更新常見錯誤文檔
- 開發出新的實現模式時更新函數使用指南
- 改進開發流程時更新最佳實踐

### 貢獻指南
- 所有更新都要有源碼檢查依據
- 提供實際的使用範例
- 包含適當的錯誤處理說明
- 保持文檔的一致性和完整性

---

**版本**: 1.0  
**作者**: Augment Agent  
**最後更新**: 2025-06-18  
**說明**: mylua 系統的通用技術參考，適用於所有 Stone Age Lua 項目
