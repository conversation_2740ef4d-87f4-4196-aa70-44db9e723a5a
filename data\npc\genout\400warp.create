NPCCREATE
# generated by npcgen.perl which is made by ringo
#
#
# infile : data/npc/seimu/400warp.gen
#line 5
{
floorid=400
borncorner=33,98,33,98
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6000|30|79
}
#line 6
{
floorid=400
borncorner=34,98,34,98
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6000|31|79
}
#line 11
{
floorid=6000
borncorner=30,80,30,80
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|400|33|99
}
#line 12
{
floorid=6000
borncorner=31,80,31,80
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|400|34|99
}
#line 20
{
floorid=400
borncorner=79,78,79,78
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6000|75|60
}
#line 21
{
floorid=400
borncorner=79,79,79,79
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6000|75|61
}
#line 22
{
floorid=400
borncorner=79,80,79,80
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6000|75|62
}
#line 26
{
floorid=6000
borncorner=76,60,76,60
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|400|80|78
}
#line 27
{
floorid=6000
borncorner=76,61,76,61
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|400|80|79
}
#line 28
{
floorid=6000
borncorner=76,62,76,62
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|400|80|80
}
#line 35
{
floorid=400
borncorner=66,45,66,45
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6000|63|28
}
#line 36
{
floorid=400
borncorner=67,45,67,45
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6000|64|28
}
#line 41
{
floorid=6000
borncorner=63,27,63,27
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|400|66|44
}
#line 42
{
floorid=6000
borncorner=64,27,64,27
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|400|67|44
}
