-- Summer Radish Pulling Event
-- Structure and style adapted from treasure202501.lua
-- Uses NetLoopFunction for periodic Radish spawning and a Create function for NPC setup.

-- Global tables for event configuration and runtime state
local event_config = {}
local radish_event_state = {
    last_radish_spawn_time = 0,
    active_radish_npc_index = -1 -- Stores the charaindex of the currently spawned Radish NPC
}

-- Configuration function, called by main()
function data()
    -- Event Settings
    event_config.MAP_ID = 1000 -- Sa Village
    event_config.SPAWN_AREA_X1 = 28
    event_config.SPAWN_AREA_Y1 = 51
    event_config.SPAWN_AREA_X2 = 109
    event_config.SPAWN_AREA_Y2 = 124
    event_config.SPAWN_INTERVAL_MS = 3 * 60 * 1000 -- 3 minutes: Interval to attempt Radish spawn
    
    event_config.RADISH_NPC_NAME = "夏日大蘿蔔"
    event_config.RADISH_NPC_METAMO = 100410 -- !!! PLACEHOLDER: Replace with actual Radish Sprite ID !!!
    event_config.RADISH_LIFESPAN_MS = 2 * 60 * 1000 -- 2 minutes: How long Radis<PERSON> stays before despawning
    
    event_config.REWARD_ITEMS = {33241, 33236} -- !!! PLACEHOLDER: Replace/Add actual reward item IDs !!!
    event_config.REWARD_CHANCE_PERCENT = 50 -- Chance (1-100) to get a reward
    
    event_config.PLAYER_REQ_REBIRTH = 4
    event_config.PLAYER_REQ_LEVEL = 120
    event_config.DAILY_LIMIT_CHAR_VAR = "SummerRadishDayJ" -- Character variable to store os.date("%j") for daily limit

    -- Seed random numbers, as in treasure202501.lua
    math.randomseed(tostring(os.time()):reverse():sub(1, 7))
end

-- Main function, called by game engine when script is loaded
function main()
    data() -- Load all event configurations
    -- Initialize last_radish_spawn_time to allow a spawn attempt after the first interval
    if event_config.SPAWN_INTERVAL_MS then
        radish_event_state.last_radish_spawn_time = os.time() * 1000 - event_config.SPAWN_INTERVAL_MS + 10000 -- ~10s for first potential spawn
    else
        radish_event_state.last_radish_spawn_time = os.time() * 1000
    end    
    print("Summer Radish Event: Initialized via main(). NetLoopFunction will call Create() for spawning.")
end

-- --- Functions for the Radish NPC (as in treasure202501.lua) --- --
-- These are assigned to Radish NPCs by the Create() function.

function Loop(meindex) -- Renamed from Radish_Loop
    -- Check if the Radish's lifespan has expired
    local creation_time_ms = char.getInt(meindex, "RadishCreationTime") -- Set by Create()
    if os.time() * 1000 - creation_time_ms >= event_config.RADISH_LIFESPAN_MS then
        npc.DeleteNpc(meindex) -- Delete the Radish NPC
        if radish_event_state.active_radish_npc_index == meindex then
            radish_event_state.active_radish_npc_index = -1 -- Clear the event's tracker
        end
    end
end

function Talked(meindex, talkerindex, szMes, color) -- Renamed from Radish_Talked
    if npc.isFaceToFace(meindex, talkerindex) ~= 1 then
        return -- Player must be facing the Radish
    end

    local player_name = char.getChar(talkerindex, "姓名") or "勇敢的冒險者"

    -- Check player requirements (Rebirth and Level)
    if char.getInt(talkerindex, "轉生") < event_config.PLAYER_REQ_REBIRTH or 
       (char.getInt(talkerindex, "轉生") == event_config.PLAYER_REQ_REBIRTH and char.getInt(talkerindex, "等級") < event_config.PLAYER_REQ_LEVEL) then
        char.TalkToCli(talkerindex, meindex, "你的實力還不夠拔起這根蘿蔔呢！(需四轉120級以上)", "白色")
        return
    end

    -- Check daily reward limit using day of the year (similar to treasure202501.lua)
    local current_day_of_year = tonumber(os.date("%j")) -- os.date("%j") returns day of year as string
    if char.getInt(talkerindex, event_config.DAILY_LIMIT_CHAR_VAR) == current_day_of_year then
        char.TalkToCli(talkerindex, meindex, "你今天已經拔過獎勵蘿蔔了，明天再來吧！", "白色")
        return
    end

    -- Check for empty item slot
    if char.GetEmptyItemBoxNum(talkerindex) < 1 then
        char.TalkToCli(talkerindex, meindex, "[系統]: 包包空間不夠哦", "紅色")
        return
    end

    -- Attempt to give reward based on chance
    if math.random(100) <= event_config.REWARD_CHANCE_PERCENT then
        if #event_config.REWARD_ITEMS > 0 then
            local reward_item_id = event_config.REWARD_ITEMS[math.random(#event_config.REWARD_ITEMS)]
            npc.AddItem(talkerindex, reward_item_id)
            char.setInt(talkerindex, event_config.DAILY_LIMIT_CHAR_VAR, current_day_of_year) -- Mark as rewarded for today
            
            local item_name = item.getNameFromNumber(reward_item_id) or ("物品ID:" .. reward_item_id)
            char.TalkToCli(talkerindex, meindex, "恭喜你拔到了蘿蔔！獲得了 " .. item_name .. "！", "綠色")
            char.Broadcast(-1, "[活動] 恭喜玩家 " .. player_name .. " 成功拔起了夏日大蘿蔔，獲得了 " .. item_name .. "！", "黃色")
        else
            char.TalkToCli(talkerindex, meindex, "蘿蔔被拔起來了，但裡面是空的！(系統獎勵未配置)", "白色")
        end
    else
        char.TalkToCli(talkerindex, meindex, "哎呀，差一點點！蘿蔔太滑溜，溜走了！", "白色")
    end
    
    -- Radish is always deleted after interaction
    npc.DeleteNpc(meindex)
    if radish_event_state.active_radish_npc_index == meindex then
        radish_event_state.active_radish_npc_index = -1 -- Clear the event's tracker
    end
end

function WindowTalked(meindex, talkerindex, seqno, select, data) -- Renamed from Radish_WindowTalked
    return -- This event type is not used for the Radish NPC
end

-- Create function: Called by NetLoopFunction to instantiate and configure a Radish NPC.
-- Mirrors the Create function in treasure202501.lua~ for individual NPC setup.
function Create(npc_name, npc_metamo, map_id, x, y, dir)
    local radish_charaindex = npc.CreateNpc(npc_name, npc_metamo, map_id, x, y, dir)

    if radish_charaindex == -1 then
        print("Summer Radish (Create): Failed to create Radish NPC - " .. npc_name .. " at map " .. map_id .. ":" .. x .. "," .. y)
        return -1 -- Indicate failure
    end

    -- Assign functions to the newly created Radish NPC
    char.setFunctionPointer(radish_charaindex, "對話事件", "Talked", "") -- Uses the renamed Talked function
    char.setFunctionPointer(radish_charaindex, "窗口事件", "WindowTalked", "") -- Uses the renamed WindowTalked function
    char.setFunctionPointer(radish_charaindex, "循環事件", "Loop", "")    -- Uses the renamed Loop function
    char.setInt(radish_charaindex, "循環事件時間", 2000) -- Radish checks its loop every 2s (adjust as needed)
    char.setInt(radish_charaindex, "RadishCreationTime", os.time() * 1000) -- Store creation time in ms
    
    print("Summer Radish (Create): Successfully created Radish NPC '" .. npc_name .. "' (ID: " .. radish_charaindex .. ")")
    return radish_charaindex -- Return charaindex of the configured Radish NPC
end

-- --- NetLoopFunction: Core timing for the event --- --
-- This function is expected to be called periodically by the game engine.
function NetLoopFunction()
    -- Check if it's time to spawn a new Radish
    if os.time() * 1000 - radish_event_state.last_radish_spawn_time >= event_config.SPAWN_INTERVAL_MS then
        -- Check if a Radish already exists (and is valid, char.CHECK)
        if radish_event_state.active_radish_npc_index ~= -1 and char.CHECK(radish_event_state.active_radish_npc_index) == true then
            radish_event_state.last_radish_spawn_time = os.time() * 1000 -- Radish exists, reset timer and wait
            return
        end
        radish_event_state.active_radish_npc_index = -1 -- Clear any old/invalid index

        -- Determine random spawn location within configured area
        local spawn_x = math.random(event_config.SPAWN_AREA_X1, event_config.SPAWN_AREA_X2)
        local spawn_y = math.random(event_config.SPAWN_AREA_Y1, event_config.SPAWN_AREA_Y2)
        local spawn_dir = math.random(0, 7) -- Random direction

        -- Call the Create function to make the Radish NPC
        local new_radish_index = Create(event_config.RADISH_NPC_NAME, event_config.RADISH_NPC_METAMO, event_config.MAP_ID, spawn_x, spawn_y, spawn_dir)
        
        if new_radish_index == -1 then
            -- Create function already prints an error, so we just reset spawn timer here
            radish_event_state.last_radish_spawn_time = os.time() * 1000 -- Reset timer to try again later
            return
        end
        
        radish_event_state.active_radish_npc_index = new_radish_index -- Track the newly spawned Radish
        radish_event_state.last_radish_spawn_time = os.time() * 1000 -- Update last spawn time
        
        -- Announce Radish spawn on the map
        char.Broadcast(event_config.MAP_ID, "[活動]: 一根肥美的夏日大蘿蔔出現在薩村 (".. spawn_x .. "," .. spawn_y .. ") 附近了！快去找找看！", "黃色")
        print("Summer Radish (NetLoopFunction): Called Create() for Radish (ID: " .. new_radish_index .. ") at " .. event_config.MAP_ID .. ":" .. spawn_x .. "," .. spawn_y)
    end
end

-- Note on GM Commands: 
-- The mechanism for GM commands in treasure202501.lua~ is not visible in the provided snippet.
-- If GM commands are needed for this event, their implementation should be based on how they 
-- are handled in treasure202501.lua~ or other similar scripts in your environment.
