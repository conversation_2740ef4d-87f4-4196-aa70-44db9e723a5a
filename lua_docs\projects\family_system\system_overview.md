# 家族建設系統 - 系統概述

## 系統簡介

家族建設系統是一個基於 Stone Age 遊戲內建家族系統的擴展功能，允許族長申請家族建設，為家族成員提供共享倉庫等福利。

## 核心功能

### 1. 家族建設申請
- **前提條件**: 必須已加入家族
- **申請權限**: 只有族長可以申請家族建設
- **起始等級**: Lv.1
- **資源消耗**:
  - 聲望: 500,000
  - 鑽石: 800個
  - 珍珠: 800個
  - 銅條: 800個
  - 鐵條: 800個

### 2. 家族倉庫系統
- **道具倉庫**: 30格共享道具存儲空間
- **寵物倉庫**: 30格共享寵物存儲空間
- **使用權限**: 所有家族成員都可以存取
- **安全機制**: 禁止存儲特殊道具，防止濫用

### 3. 權限管理
- **族長權限**: 申請建設、升級建設、使用倉庫、查看資訊
- **族員權限**: 使用倉庫、查看資訊 (建設開通後)
- **非家族成員**: 只能查看建設條件

## 技術架構

### 文件結構
```
data/ablua/npc/system/
├── family_system.lua      # 主要家族建設系統
└── familypool.lua         # 家族倉庫系統
```

### 數據庫設計
```sql
-- 家族建設表
CREATE TABLE family_constructions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    family_index INT NOT NULL UNIQUE,
    leader_name VARCHAR(50) NOT NULL,
    construction_level INT DEFAULT 1,
    create_time DATETIME NOT NULL,
    warehouse_slots INT DEFAULT 30,
    INDEX idx_family_index (family_index)
);

-- 家族倉庫表
CREATE TABLE family_warehouse (
    id INT AUTO_INCREMENT PRIMARY KEY,
    family_index INT NOT NULL,
    slot_id INT NOT NULL DEFAULT 0,
    item_id INT DEFAULT NULL,
    item_count INT DEFAULT 0,
    item_data TEXT DEFAULT NULL,
    slot_count INT DEFAULT 30,
    INDEX idx_family_slot (family_index, slot_id)
);

-- 家族倉庫寵物表
CREATE TABLE family_warehouse_pets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    family_index INT NOT NULL,
    slot_id INT NOT NULL,
    pet_data TEXT NOT NULL,
    INDEX idx_family_pet_slot (family_index, slot_id)
);
```

## 系統特色

### 1. 基於內建家族系統
- 使用遊戲內建的家族系統 (`char.getInt(talkerindex, "家族索引")`)
- 不創建新的家族，而是擴展現有功能
- 確保與遊戲原有機制的兼容性

### 2. 簡化的申請流程
- 移除了複雜的隊伍檢查
- 只需要族長身份和足夠資源
- 清晰的權限分層設計

### 3. 安全的倉庫系統
- 參考 pool.lua 的實現模式
- 禁止存放特殊道具清單
- 完整的錯誤處理機制

## 使用流程

### 申請家族建設
1. **加入家族**: 必須先通過遊戲內建系統加入家族
2. **成為族長**: 只有族長才能申請建設
3. **準備資源**: 確保有足夠的聲望和道具
4. **與NPC對話**: 找到家族建設管理員NPC
5. **選擇申請**: 點擊"申請家族建設"
6. **確認申請**: 檢查條件後確認建設

### 使用家族倉庫
1. **進入倉庫**: 與家族管理員NPC對話 → 選擇"家族倉庫"
2. **道具操作**:
   - 選擇"道具倉庫"打開道具存取界面
   - 點擊背包道具可存入家族倉庫
   - 點擊倉庫道具可取出到背包
3. **寵物操作**:
   - 選擇"寵物倉庫"打開寵物存取界面
   - 點擊身上寵物可存入家族倉庫
   - 點擊倉庫寵物可取出到身上

## 開發狀態

- ✅ 家族建設申請
- ✅ 家族資訊查看
- ✅ 家族倉庫功能（道具和寵物存取）
- ✅ NPC 創建和事件處理
- ✅ 窗口函數參數修正
- 🚧 家族升級系統
- 🚧 家族技能系統

## 注意事項

### ⚠️ 使用限制
1. **道具編號**: 請根據實際遊戲設定調整道具編號
2. **權限控制**: 只有族長可以申請家族建設
3. **資源消耗**: 申請成功後資源會被立即消耗
4. **倉庫限制**: 某些特殊道具無法存入倉庫

## 技術要求

### 服務器環境
- Stone Age 服務器
- mylua 系統支持
- MySQL 數據庫

### 編碼要求
- 使用 Big5 編碼確保中文顯示正常
- 遵循 mylua 系統的函數調用規範
- 參考現有系統腳本的實現模式

---

**版本**: 1.4  
**作者**: Augment Agent  
**最後更新**: 2025-06-18
