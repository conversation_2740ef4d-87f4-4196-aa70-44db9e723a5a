NPCCREATE
# generated by npcgen.perl which is made by ringo
#
#
# infile : data/npc/jaruga/warp_y2.gen
#line 23
{
floorid=3000
borncorner=68,103,68,103
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3001|15|21
}
# between
{
floorid=3001
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|68|103
}
#line 24
{
floorid=3001
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|68|103
}
#line 26
{
floorid=3000
borncorner=71,116,71,116
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3002|10|15
}
# between
{
floorid=3002
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|71|116
}
#line 27
{
floorid=3002
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|71|116
}
#line 29
{
floorid=3000
borncorner=77,91,77,91
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3003|15|21
}
# between
{
floorid=3003
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|77|91
}
#line 30
{
floorid=3003
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|77|91
}
#line 32
{
floorid=3000
borncorner=107,92,107,92
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3004|10|15
}
# between
{
floorid=3004
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|107|92
}
#line 33
{
floorid=3000
borncorner=107,93,107,93
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3004|10|16
}
# between
{
floorid=3004
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|107|93
}
#line 35
{
floorid=3000
borncorner=83,106,83,106
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3005|10|15
}
# between
{
floorid=3005
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|83|106
}
#line 36
{
floorid=3005
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|83|106
}
#line 38
{
floorid=3000
borncorner=90,60,90,60
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3006|20|21
}
# between
{
floorid=3006
borncorner=20,21,20,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|90|60
}
#line 39
{
floorid=3006
borncorner=21,21,21,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|90|60
}
#line 46
{
floorid=3000
borncorner=112,80,112,80
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3020|4|12
}
# between
{
floorid=3020
borncorner=4,12,4,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|112|80
}
#line 47
{
floorid=3000
borncorner=112,81,112,81
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3020|4|12
}
#line 49
{
floorid=3008
borncorner=13,20,13,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3020|12|9
}
#line 50
{
floorid=3008
borncorner=13,21,13,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3020|12|9
}
#line 52
{
floorid=3022
borncorner=6,14,6,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3020|12|9
}
#line 53
{
floorid=3022
borncorner=7,14,7,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3020|12|9
}
#line 54
{
floorid=3022
borncorner=8,14,8,14
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3020|12|9
}
#line 58
{
floorid=3000
borncorner=105,66,105,66
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3011|15|21|M
}
# between
{
floorid=3011
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|105|66
}
#line 59
{
floorid=3011
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|105|66
}
#line 61
{
floorid=3000
borncorner=106,66,106,66
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3014|16|21|A
}
# between
{
floorid=3014
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|106|66
}
#line 62
{
floorid=3014
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|106|66
}
#line 66
{
floorid=3000
borncorner=82,72,82,72
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3012|15|21
}
# between
{
floorid=3012
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|82|72
}
#line 67
{
floorid=3000
borncorner=83,72,83,72
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3012|16|21
}
# between
{
floorid=3012
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|83|72
}
#line 71
{
floorid=3007
borncorner=50,69,50,69
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|92|86
}
#line 72
{
floorid=3007
borncorner=49,69,49,69
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|92|86
}
#line 76
{
floorid=3000
borncorner=107,110,107,110
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3009|10|25
}
# between
{
floorid=3009
borncorner=10,25,10,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|107|110
}
#line 77
{
floorid=3000
borncorner=107,111,107,111
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3009|10|26
}
# between
{
floorid=3009
borncorner=10,26,10,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3000|107|111
}
#line 83
{
floorid=4000
borncorner=99,65,99,65
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4001|10|15
}
# between
{
floorid=4001
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|99|65
}
#line 84
{
floorid=4000
borncorner=99,66,99,66
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4001|10|16
}
# between
{
floorid=4001
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|99|66
}
#line 86
{
floorid=4000
borncorner=65,46,65,46
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4002|15|21
}
# between
{
floorid=4002
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|65|46
}
#line 87
{
floorid=4000
borncorner=66,46,66,46
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4002|16|21
}
# between
{
floorid=4002
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|66|46
}
#line 89
{
floorid=4000
borncorner=99,72,99,72
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4003|10|15
}
# between
{
floorid=4003
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|99|72
}
#line 90
{
floorid=4000
borncorner=99,73,99,73
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4003|10|16
}
# between
{
floorid=4003
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|99|73
}
#line 92
{
floorid=4000
borncorner=76,47,76,47
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4004|15|21
}
# between
{
floorid=4004
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|76|47
}
#line 93
{
floorid=4000
borncorner=77,47,77,47
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4004|16|21
}
# between
{
floorid=4004
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|77|47
}
#line 95
{
floorid=4000
borncorner=100,80,100,80
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4005|10|15
}
# between
{
floorid=4005
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|100|80
}
#line 96
{
floorid=4005
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|100|80
}
#line 98
{
floorid=4000
borncorner=80,90,80,90
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4006|10|20
}
# between
{
floorid=4006
borncorner=10,20,10,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|80|90
}
#line 99
{
floorid=4000
borncorner=80,91,80,91
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4006|10|21
}
# between
{
floorid=4006
borncorner=10,21,10,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|80|91
}
#line 106
{
floorid=4000
borncorner=55,93,55,93
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4011|15|21|M
}
# between
{
floorid=4011
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|55|93
}
#line 107
{
floorid=4011
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|55|93
}
#line 109
{
floorid=4000
borncorner=56,93,56,93
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4014|16|21|A
}
# between
{
floorid=4014
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|56|93
}
#line 110
{
floorid=4014
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|56|93
}
#line 114
{
floorid=4007
borncorner=47,76,47,76
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|87|61
}
#line 115
{
floorid=4007
borncorner=48,76,48,76
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|87|61
}
#line 116
{
floorid=4007
borncorner=49,76,49,76
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|87|61
}
#line 120
{
floorid=4000
borncorner=82,48,82,48
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4012|15|21
}
# between
{
floorid=4012
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|82|48
}
#line 121
{
floorid=4012
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|82|48
}
#line 125
{
floorid=4000
borncorner=40,73,40,73
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4009|10|25
}
# between
{
floorid=4009
borncorner=10,25,10,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|40|73
}
#line 126
{
floorid=4000
borncorner=40,74,40,74
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4009|10|26
}
# between
{
floorid=4009
borncorner=10,26,10,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|4000|40|74
}
#line 132
{
floorid=3100
borncorner=44,69,44,69
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3103|15|21
}
# between
{
floorid=3103
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3100|44|69
}
#line 133
{
floorid=3103
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3100|44|69
}
#line 135
{
floorid=3100
borncorner=61,72,61,72
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3105|10|15
}
# between
{
floorid=3105
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3100|61|72
}
#line 136
{
floorid=3105
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3100|61|72
}
#line 138
{
floorid=3100
borncorner=41,78,41,78
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3109|10|25
}
# between
{
floorid=3109
borncorner=10,25,10,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3100|41|78
}
#line 139
{
floorid=3100
borncorner=41,79,41,79
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3109|10|26
}
# between
{
floorid=3109
borncorner=10,26,10,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3100|41|79
}
#line 141
{
floorid=3100
borncorner=59,62,59,62
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3106|10|20
}
# between
{
floorid=3106
borncorner=10,20,10,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3100|59|62
}
#line 142
{
floorid=3100
borncorner=59,63,59,63
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3106|10|21
}
# between
{
floorid=3106
borncorner=10,21,10,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3100|59|63
}
#line 144
{
floorid=3100
borncorner=52,84,52,84
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3104|10|15
}
# between
{
floorid=3104
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3100|52|84
}
#line 145
{
floorid=3100
borncorner=52,85,52,85
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3104|10|16
}
# between
{
floorid=3104
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3100|52|85
}
#line 149
{
floorid=3100
borncorner=44,55,44,55
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3110|15|21
}
# between
{
floorid=3110
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3100|44|55
}
#line 150
{
floorid=3110
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3100|44|55
}
#line 161
{
floorid=3200
borncorner=48,43,48,43
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3203|15|21
}
# between
{
floorid=3203
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3200|48|43
}
#line 162
{
floorid=3200
borncorner=49,43,49,43
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3203|16|21
}
# between
{
floorid=3203
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3200|49|43
}
#line 164
{
floorid=3200
borncorner=58,73,58,73
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3204|10|15
}
# between
{
floorid=3204
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3200|58|73
}
#line 165
{
floorid=3200
borncorner=58,74,58,74
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3204|10|16
}
# between
{
floorid=3204
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3200|58|74
}
#line 167
{
floorid=3200
borncorner=38,43,38,43
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3205|15|21
}
# between
{
floorid=3205
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3200|38|43
}
#line 168
{
floorid=3200
borncorner=39,43,39,43
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3205|16|21
}
# between
{
floorid=3205
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3200|39|43
}
#line 173
{
floorid=3200
borncorner=61,46,61,46
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3209|10|25
}
# between
{
floorid=3209
borncorner=10,25,10,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3200|61|46
}
#line 174
{
floorid=3200
borncorner=61,47,61,47
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3209|10|26
}
# between
{
floorid=3209
borncorner=10,26,10,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3200|61|47
}
#line 176
{
floorid=3200
borncorner=53,64,53,64
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3206|10|20
}
# between
{
floorid=3206
borncorner=10,20,10,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3200|53|64
}
#line 177
{
floorid=3206
borncorner=10,21,10,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3200|53|64
}
#line 188
{
floorid=3300
borncorner=51,60,51,60
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3303|15|20
}
# between
{
floorid=3303
borncorner=15,20,15,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3300|51|60
}
#line 189
{
floorid=3303
borncorner=16,20,16,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3300|51|60
}
#line 191
{
floorid=3300
borncorner=48,64,48,64
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3304|11|15
}
# between
{
floorid=3304
borncorner=11,15,11,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3300|48|64
}
#line 192
{
floorid=3300
borncorner=48,65,48,65
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3304|11|16
}
# between
{
floorid=3304
borncorner=11,16,11,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3300|48|65
}
#line 194
{
floorid=3300
borncorner=59,45,59,45
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3305|11|15
}
# between
{
floorid=3305
borncorner=11,15,11,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3300|59|45
}
#line 195
{
floorid=3305
borncorner=11,16,11,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3300|59|45
}
#line 197
{
floorid=3300
borncorner=54,43,54,43
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3306|15|20
}
# between
{
floorid=3306
borncorner=15,20,15,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3300|54|43
}
#line 198
{
floorid=3306
borncorner=16,20,16,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3300|54|43
}
#line 203
{
floorid=3300
borncorner=54,70,54,70
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3309|11|15
}
# between
{
floorid=3309
borncorner=11,15,11,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3300|54|70
}
#line 204
{
floorid=3300
borncorner=54,71,54,71
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3309|11|16
}
# between
{
floorid=3309
borncorner=11,16,11,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3300|54|71
}
#line 215
{
floorid=3400
borncorner=53,49,53,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3403|15|21
}
# between
{
floorid=3403
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3400|53|49
}
#line 216
{
floorid=3400
borncorner=54,49,54,49
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3403|16|21
}
# between
{
floorid=3403
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3400|54|49
}
#line 218
{
floorid=3400
borncorner=47,57,47,57
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3404|15|21
}
# between
{
floorid=3404
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3400|47|57
}
#line 219
{
floorid=3400
borncorner=48,57,48,57
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3404|16|21
}
# between
{
floorid=3404
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3400|48|57
}
#line 221
{
floorid=3400
borncorner=59,54,59,54
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3405|10|11
}
# between
{
floorid=3405
borncorner=10,11,10,11
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3400|59|54
}
#line 222
{
floorid=3400
borncorner=59,55,59,55
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3405|10|12
}
# between
{
floorid=3405
borncorner=10,12,10,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3400|59|55
}
#line 224
{
floorid=3400
borncorner=51,73,51,73
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3409|10|25
}
# between
{
floorid=3409
borncorner=10,25,10,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3400|51|73
}
#line 225
{
floorid=3400
borncorner=51,74,51,74
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3409|10|26
}
# between
{
floorid=3409
borncorner=10,26,10,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3400|51|74
}
#line 227
{
floorid=3400
borncorner=54,62,54,62
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3406|10|15
}
# between
{
floorid=3406
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3400|54|62
}
#line 228
{
floorid=3406
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3400|54|62
}
#line 232
{
floorid=3400
borncorner=59,75,59,75
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3412|10|15
}
# between
{
floorid=3412
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3400|59|75
}
#line 233
{
floorid=3400
borncorner=59,76,59,76
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3412|10|16
}
# between
{
floorid=3412
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|3400|59|76
}
#line 239
{
floorid=200
borncorner=328,660,328,660
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|201|6|19
}
# between
{
floorid=201
borncorner=6,19,6,19
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|200|328|660
}
#line 240
{
floorid=200
borncorner=328,661,328,661
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|201|6|20
}
# between
{
floorid=201
borncorner=6,20,6,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|200|328|661
}
