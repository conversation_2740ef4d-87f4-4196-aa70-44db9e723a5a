NPCCREATE
# generated by npcgen.perl which is made by ringo
#
#
# infile : data/npc/giiru/town/warp_y3.gen
#line 23
{
floorid=5000
borncorner=52,30,52,30
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5001|10|15
}
# between
{
floorid=5001
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5000|52|30
}
#line 24
{
floorid=5001
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5000|52|30
}
#line 26
{
floorid=5000
borncorner=51,36,51,36
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5003|10|15
}
# between
{
floorid=5003
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5000|51|36
}
#line 27
{
floorid=5003
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5000|51|36
}
#line 29
{
floorid=5000
borncorner=48,47,48,47
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5005|10|15
}
# between
{
floorid=5005
borncorner=10,15,10,15
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5000|48|47
}
#line 30
{
floorid=5005
borncorner=10,16,10,16
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|5000|48|47
}
