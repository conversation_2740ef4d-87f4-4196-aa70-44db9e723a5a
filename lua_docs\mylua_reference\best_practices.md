# mylua 開發最佳實踐

## 🔍 源碼檢查習慣

### 必須檢查的項目
1. **函數用法**: 檢查 mylua/*.c 確認函數參數和返回值
2. **變數名稱**: 檢查 mylua/charbase.c 確認 CHAR_* 常數對應的實際變數名
3. **枚舉常數**: 檢查 include/*.h 確認枚舉值的具體數值
4. **註冊名稱**: 檢查 luaL_Reg 結構確認在 Lua 中的函數名稱
5. **工具函數引入**: 只引入實際使用的工具函數，避免不必要的依賴

### 檢查實例

#### ✅ 正確的函數用法檢查
```bash
# 檢查 other.CallFunction
grep -r "CallFunction" mylua/otherbase.c
# 結果: 確認參數順序為 (funcname, filename, args_table)

# 檢查 char.countitem
grep -r "countitem" mylua/charbase.c
# 結果: 確認參數為 (talkerindex, itemid)

# 檢查 npc.DelItemNum
grep -r "DelItemNum" mylua/npcbase.c
# 結果: 確認參數為 (talkerindex, "itemid,count")

# 檢查 lssproto.windows
grep -r "windows_send" mylua/lssprotobase.c
# 結果: 確認參數為 (talkerindex, windowtype, buttontype, seqno, objindex, data)
```

#### ✅ 正確的變數名稱檢查
```bash
# 檢查家族相關變數
grep -r "家族" mylua/charbase.c
# 結果: 
# - "家族索引" (CHAR_FMINDEX)
# - "家族地位" (CHAR_FMLEADERFLAG) 
# - "家族" (CHAR_FMNAME)
```

## 🎯 函數使用最佳實踐

### 1. 道具操作
```lua
-- ✅ 推薦方式 (已檢查 mylua/charbase.c 和 mylua/npcbase.c)
function checkItemCount(talkerindex, itemid, required_count)
    local count = char.countitem(talkerindex, itemid)
    return count >= required_count, count
end

function consumeItem(talkerindex, itemid, required_count)
    local result = npc.DelItemNum(talkerindex, itemid .. "," .. required_count)
    return result == 1
end

-- ❌ 不推薦的手動遍歷方式
-- 複雜、容易出錯、效率低
```

### 2. 家族系統變數
```lua
-- ✅ 正確的變數名稱 (已檢查 mylua/charbase.c)
local family_status = char.getInt(talkerindex, "家族地位")  -- CHAR_FMLEADERFLAG
local family_index = char.getInt(talkerindex, "家族索引")   -- CHAR_FMINDEX
local family_name = char.getChar(talkerindex, "家族")       -- CHAR_FMNAME

-- ❌ 錯誤的變數名稱
-- char.getInt(talkerindex, "家族編號")  -- 此變數不存在
```

### 3. 跨文件函數調用
```lua
-- ✅ 正確方式 (已檢查 mylua/otherbase.c)
other.CallFunction("functionName", "./filename.lua", {param1, param2})

-- ❌ 錯誤方式
-- dofile("./file.lua"); functionName(param)  -- 不安全
```

### 4. 窗口系統使用
```lua
-- ✅ 正確方式 (已檢查 mylua/lssprotobase.c，參考 pool.lua)
lssproto.windows(talkerindex, 1103, 8, 0, char.getWorkInt(npcindex, "對象"), data)

-- ❌ 錯誤方式
-- lssproto.windows(talkerindex, 1103, 8, 0, 999999, data)  -- 錯誤的對象索引
```

## 🏗️ 權限控制設計

### 權限分層原則
```lua
-- 家族地位常數 (已檢查 include/char_base.h)
-- FMMEMBER_NONE = -1     (沒有家族)
-- FMMEMBER_MEMBER = 1    (家族成員)
-- FMMEMBER_APPLY = 2     (申請入族)
-- FMMEMBER_LEADER = 3    (族長)
-- FMMEMBER_ELDER = 4     (長老)

if family_status == 3 then  -- FMMEMBER_LEADER
    -- 族長專用功能：
    -- - 申請家族建設 (不需要隊伍)
    -- - 升級建設
    -- - 使用倉庫
    -- - 查看資訊
elseif family_status >= 1 then  -- 有家族成員
    -- 族員可用功能：
    -- - 使用倉庫 (建設開通後)
    -- - 查看資訊
else  -- FMMEMBER_NONE
    -- 無家族：
    -- - 只能查看建設條件
end
```

### 簡化的申請條件
```lua
-- ✅ 正確的申請條件檢查 (已簡化)
function checkApplicationRequirements(talkerindex)
    -- 只檢查必要條件：
    -- 1. 是否有家族
    -- 2. 是否為族長
    -- 3. 家族是否已有建設
    -- 4. 資源是否足夠
    
    -- 不需要檢查：
    -- - 隊伍成員數量
    -- - 隊長身份
end

-- ❌ 過度複雜的檢查 (已移除)
-- function checkPartyMemberCount() -- 不需要
```

### UI 設計原則
1. **明確顯示權限限制**: 在選單中標示 "(族長專用)" 等提示
2. **友好的錯誤提示**: 告知用戶為什麼無法使用某功能
3. **分層式選單**: 根據權限動態生成選單選項

## 📊 數據庫設計

### 表結構設計原則
```sql
-- 使用遊戲內建的家族索引，而不是自定義ID
CREATE TABLE family_constructions (
    family_index INT NOT NULL UNIQUE,  -- 對應 char.getInt(talkerindex, "家族索引")
    -- 其他欄位...
);
```

### 查詢優化
1. **建立適當索引**: `INDEX idx_family_index (family_index)`
2. **避免複雜查詢**: 使用簡單的 WHERE 條件
3. **數據一致性**: 使用遊戲內建的家族系統作為主鍵

## 🔧 開發流程

### 1. 需求分析
- 明確功能的權限要求
- 確定需要使用的 mylua 函數
- 設計數據庫表結構

### 2. 源碼檢查
- 檢查所有要使用的函數和變數
- 確認參數格式和返回值
- 記錄檢查結果在代碼註釋中

### 3. 實現開發
- 使用檢查過的正確函數
- 實現適當的權限控制
- 添加友好的用戶提示

### 4. 測試驗證
- 測試不同權限級別的功能
- 驗證錯誤處理機制
- 確認數據庫操作正確性

## 📝 文檔維護

### 必須記錄的內容
1. **源碼檢查記錄**: 在代碼中註釋檢查過的文件
2. **權限設計說明**: 在 README 中說明權限分層
3. **數據庫設計**: 記錄表結構和索引設計
4. **部署指南**: 提供完整的部署步驟

### 文檔更新原則
- 每次修改函數用法時更新檢查記錄
- 權限變更時更新權限說明
- 新增功能時更新使用說明

## 🚨 常見錯誤避免

### 1. 變數名稱錯誤
- 不要憑記憶使用變數名稱
- 每次都檢查 mylua/charbase.c 確認

### 2. 函數參數錯誤
- 不要假設函數參數順序
- 檢查 mylua/*.c 確認正確用法

### 3. 權限控制遺漏
- 每個功能都要考慮權限控制
- 提供清楚的權限錯誤提示

### 4. 跨文件調用錯誤
- 使用 other.CallFunction 而不是 dofile
- 注意參數只能是整數

### 5. 不必要的工具函數引入
- 不要盲目引入 utils.lua 等工具函數
- 只引入實際使用的函數
- 明確註釋為什麼不需要某些引入

### 6. 過度複雜的條件檢查
- 避免不必要的隊伍檢查
- 簡化申請條件，只檢查核心要求
- 專注於功能本身的邏輯

### 7. 窗口函數參數錯誤
- 不要使用隨意的數值如 999999
- 使用正確的對象索引 char.getWorkInt(npcindex, "對象")
- 參考現有系統腳本的實現

## 📋 開發檢查清單

### 使用 mylua 函數前的檢查步驟
1. ✅ 查看 mylua/*.c 源碼確認函數實現
2. ✅ 查看 include/*.h 頭文件確認常數定義
3. ✅ 確認參數類型、順序和返回值
4. ✅ 參考現有系統腳本的使用方式
5. ✅ 在代碼中註釋檢查結果
6. ✅ 記錄在文檔中供後續參考

### 腳本部署前的檢查步驟
1. ✅ 確認文件使用 Big5 編碼
2. ✅ 確認文件放在正確目錄 (data/ablua/npc/system/)
3. ✅ 確認 NPC 創建和函數指針設置
4. ✅ 確認跨文件調用方式正確
5. ✅ 確認窗口函數參數正確
6. ✅ 測試所有功能和權限控制

## 🎯 總結

這個最佳實踐指南展示了以下核心原則：

1. **源碼檢查習慣**: 每次使用 mylua 函數前都檢查源碼
2. **正確的函數用法**: 使用 char.countitem 和 npc.DelItemNum
3. **準確的變數名稱**: 使用 "家族索引" 而不是 "家族編號"
4. **清晰的權限控制**: 族長管理，族員使用
5. **簡化的邏輯設計**: 避免不必要的複雜檢查
6. **精簡的依賴管理**: 只引入實際使用的工具函數
7. **正確的窗口函數**: 使用正確的對象索引參數
8. **友好的用戶體驗**: 明確的提示和錯誤信息

遵循這些實踐可以確保代碼的可靠性、可維護性、簡潔性和用戶友好性。

---

**版本**: 1.4  
**作者**: Augment Agent  
**最後更新**: 2025-06-18  
**說明**: mylua 開發的通用最佳實踐，適用於所有 Stone Age Lua 項目
