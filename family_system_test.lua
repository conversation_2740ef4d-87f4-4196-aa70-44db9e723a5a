--=============================================
-- 家族系統測試腳本
-- 功能: 測試家族系統的各項功能
-- 作者: Augment Agent
-- 版本: 1.0
--=============================================

-- 引入家族系統
dofile("./family_system.lua")

-- 測試函數
function testFamilySystem()
    print("=== 家族系統測試開始 ===")
    
    -- 測試配置載入
    print("1. 測試配置載入...")
    data()
    print("   ✓ 配置載入成功")
    print("   - 最少成員數量: " .. family_config.MIN_MEMBERS)
    print("   - 需要聲望: " .. family_config.REQUIRED_REPUTATION)
    print("   - 倉庫格數: " .. family_config.WAREHOUSE_SLOTS)
    
    -- 測試道具ID配置
    print("2. 測試道具ID配置...")
    print("   - 鑽石ID: " .. family_config.ITEM_DIAMOND)
    print("   - 珍珠ID: " .. family_config.ITEM_PEARL)
    print("   - 銅條ID: " .. family_config.ITEM_COPPER)
    print("   - 鐵條ID: " .. family_config.ITEM_IRON)
    print("   ✓ 道具ID配置正確")
    
    -- 測試數據庫表結構檢查
    print("3. 測試數據庫連接...")
    local test_query = "SELECT 1"
    local ret = sasql.query(test_query, 1)
    if ret >= 0 then
        print("   ✓ 數據庫連接正常")
    else
        print("   ✗ 數據庫連接失敗")
    end
    
    print("=== 家族系統測試完成 ===")
end

-- 測試家族倉庫系統
function testFamilyWarehouse()
    print("=== 家族倉庫系統測試開始 ===")
    
    -- 測試配置載入
    print("1. 測試倉庫配置...")
    if family_pool_config then
        print("   ✓ 倉庫配置載入成功")
        print("   - 道具倉庫格數: " .. family_pool_config.MAX_FAMILY_ITEM_SLOTS)
        print("   - 寵物倉庫格數: " .. family_pool_config.MAX_FAMILY_PET_SLOTS)
        print("   - 禁止存儲道具數量: " .. table.getn(family_pool_config.nosaveitemlist))
    else
        print("   ✗ 倉庫配置載入失敗")
    end
    
    print("=== 家族倉庫系統測試完成 ===")
end

-- 創建測試數據庫表 (如果不存在)
function createTestTables()
    print("=== 創建測試數據庫表 ===")
    
    -- 創建家族表
    local create_families = [[
        CREATE TABLE IF NOT EXISTS families (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            leader_account VARCHAR(50) NOT NULL,
            leader_name VARCHAR(50) NOT NULL,
            level INT DEFAULT 1,
            create_time DATETIME NOT NULL,
            warehouse_slots INT DEFAULT 30,
            INDEX idx_leader (leader_account, leader_name)
        )
    ]]
    
    -- 創建家族成員表
    local create_members = [[
        CREATE TABLE IF NOT EXISTS family_members (
            id INT AUTO_INCREMENT PRIMARY KEY,
            family_id INT NOT NULL,
            account VARCHAR(50) NOT NULL,
            char_name VARCHAR(50) NOT NULL,
            role ENUM('leader', 'member') DEFAULT 'member',
            join_time DATETIME NOT NULL,
            UNIQUE KEY unique_member (account, char_name),
            INDEX idx_family (family_id),
            INDEX idx_member (account, char_name)
        )
    ]]
    
    -- 創建家族倉庫表
    local create_warehouse = [[
        CREATE TABLE IF NOT EXISTS family_warehouse (
            id INT AUTO_INCREMENT PRIMARY KEY,
            family_id INT NOT NULL,
            slot_id INT NOT NULL DEFAULT 0,
            item_id INT DEFAULT NULL,
            item_count INT DEFAULT 0,
            item_data TEXT DEFAULT NULL,
            slot_count INT DEFAULT 30,
            INDEX idx_family_slot (family_id, slot_id)
        )
    ]]
    
    -- 創建家族倉庫寵物表
    local create_warehouse_pets = [[
        CREATE TABLE IF NOT EXISTS family_warehouse_pets (
            id INT AUTO_INCREMENT PRIMARY KEY,
            family_id INT NOT NULL,
            slot_id INT NOT NULL,
            pet_data TEXT NOT NULL,
            INDEX idx_family_pet_slot (family_id, slot_id)
        )
    ]]
    
    -- 執行創建語句
    local tables = {
        {"families", create_families},
        {"family_members", create_members},
        {"family_warehouse", create_warehouse},
        {"family_warehouse_pets", create_warehouse_pets}
    }
    
    for i, table_info in ipairs(tables) do
        local table_name = table_info[1]
        local create_sql = table_info[2]
        
        print("創建表: " .. table_name)
        local ret = sasql.query(create_sql, 0)
        if ret >= 0 then
            print("   ✓ " .. table_name .. " 創建成功")
        else
            print("   ✗ " .. table_name .. " 創建失敗")
        end
    end
    
    print("=== 數據庫表創建完成 ===")
end

-- 顯示使用說明
function showUsageInstructions()
    print("=== 家族系統使用說明 ===")
    print("")
    print("1. 家族建設申請:")
    print("   - 組建30人以上隊伍")
    print("   - 準備足夠的聲望和道具")
    print("   - 與家族管理員NPC對話")
    print("   - 選擇'申請家族建設'")
    print("")
    print("2. 家族倉庫使用:")
    print("   - 與家族管理員NPC對話")
    print("   - 選擇'家族倉庫'")
    print("   - 選擇'道具倉庫'或'寵物倉庫'")
    print("   - 進行存取操作")
    print("")
    print("3. 家族管理:")
    print("   - 查看家族資訊")
    print("   - 離開家族 (族長離開會解散家族)")
    print("")
    print("=== 使用說明完成 ===")
end

-- 主測試函數
function runAllTests()
    print("開始執行家族系統完整測試...")
    print("")
    
    -- 執行各項測試
    testFamilySystem()
    print("")
    testFamilyWarehouse()
    print("")
    createTestTables()
    print("")
    showUsageInstructions()
    print("")
    
    print("所有測試執行完成！")
    print("請檢查上述輸出，確認系統配置正確。")
end

-- 如果直接執行此腳本，運行所有測試
if arg and arg[0] and string.find(arg[0], "family_system_test.lua") then
    runAllTests()
end
