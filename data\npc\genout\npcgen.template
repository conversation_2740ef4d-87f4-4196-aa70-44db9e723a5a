NPCTEMPLATE
#door
{
templatename=npcgen_door
makeatnobody=1
name=
makeatnosee=1
graphicname=2163
functionset=Door
}

#passdoor
{
templatename=npcgen_passdoor
makeatnobody=1
makeatnosee=1
name=
graphicname=2163
functionset=Door
}

#titledoor
{
templatename=npcgen_titledoor
makeatnobody=1
makeatnosee=1
name=
graphicname=2163
functionset=Door
}

# healer
{
templatename=npcgen_healer
makeatnobody=1
makeatnosee=1
name=
graphicname=SPR_kkanngof
loopfunctime=4000
functionset=Healer
}
# healer
{
templatename=npcgen_winhealer
makeatnobody=1
makeatnosee=1
name=
graphicname=SPR_kkanngof
loopfunctime=4000
functionset=WindowHealer
}

# dengonban
{
templatename=npcgen_dengon
makeatnobody=1
makeatnosee=1
name=
graphicname=2204
type=SPR_sslma_1
functionset=Dengon
}
# oldman
{
templatename=npcgen_oldman
makeatnobody=1
makeatnosee=1
name=
graphicname=SPR_kbouzu
functionset=Oldman
}
# savepoint
{
templatename=npcgen_savepoint
makeatnobody=1
makeatnosee=1
name=
graphicname=16024
functionset=SavePoint
}

# shop
{
templatename=npcgen_shop
makeatnobody=1
name=
makeatnosee=1
graphicname=SPR_chr02
functionset=ItemShop
}
# limitbuyinshop
{
templatename=npcgen_limitshop
makeatnobody=1
makeatnosee=1
name=
graphicname=SPR_chr02
functionset=ItemShop
}
# poolitemshop
{
templatename=npcgen_poolitemshop
makeatnobody=1
makeatnosee=1
name=
graphicname=SPR_chr02
functionset=PoolItemShop
}

# warpman
{
templatename=npcgen_warpman
makeatnobody=1
makeatnosee=1
name=
graphicname=SPR_chr02
functionset=WarpMan
}

# fmwarpman CoolFish: Family 2001/6/6
{
templatename=npcgen_fmwarpman
makeatnobody=1
makeatnosee=1
name=
graphicname=SPR_chr02
functionset=FMWarpMan
}

# fmpkman CoolFish: Family 2001/7/4
{
templatename=npcgen_fmpkman
makeatnobody=1
makeatnosee=1
name=
graphicname=SPR_chr02
functionset=FMPKMan
}

# fmpkcallman CoolFish: Family 2001/7/13
{
templatename=npcgen_fmpkcallman
makeatnobody=1
makeatnosee=1
name=
graphicname=SPR_chr02
functionset=FMPKCallMan
}

# janken
{
templatename=npcgen_janken
makeatnobody=1
makeatnosee=1
name=
graphicname=SPR_chr02
functionset=Janken
}
# quiz
{
templatename=npcgen_quiz
makeatnobody=1
makeatnosee=1
name=
graphicname=SPR_chr02
functionset=Quiz
}


# petskillshop
{
templatename=npcgen_petskillshop
makeatnobody=1
name=
makeatnosee=1
graphicname=SPR_chr02
functionset=PetSkillShop
}

# petshop
{
templatename=npcgen_petshop
makeatnobody=1
name=
makeatnosee=1
graphicname=SPR_chr02
functionset=PetShop
}

# man
{
templatename=npcgen_man
makeatnobody=1
makeatnosee=1
name=
graphicname=SPR_chr03
functionset=TownPeople
}
# timeman
{
templatename=npcgen_timeman
makeatnobody=1
makeatnosee=1
name=
graphicname=SPR_chr03
functionset=TimeMan
}

# warp
{
templatename=npcgen_warp
makeatnobody=1
name=
makeatnosee=1
functionset=Warp
}

# action
{
templatename=npcgen_action
makeatnobody=1
name=
makeatnosee=1
functionset=Action
}

# charm
{
templatename=npcgen_charm
makeatnobody=1
name=
makeatnosee=1
functionset=Charm
}

# blacksmith
{
templatename=npcgen_blacksmith
makeatnobody=1
makeatnosee=1
name=
graphicname=SPR_chr03
functionset=BlackSmith
}
# identifier
{
templatename=npcgen_identifier
makeatnobody=1
makeatnosee=1
name=
graphicname=SPR_chr03
functionset=Identifier
}
# msg
{
templatename=npcgen_msg
makeatnobody=1
makeatnosee=1
name=
graphicname=SPR_chr03
functionset=Msg
}
# msg2
{
templatename=npcgen_msg2
makeatnobody=1
makeatnosee=1
name=
graphicname=SPR_chr03
functionset=Msg2
}
# mic
{
templatename=npcgen_mic
makeatnobody=1
makeatnosee=1
name=
graphicname=SPR_chr03
functionset=Mic
}

# signboard
{
templatename=npcgen_signboard
makeatnobody=1
makeatnosee=1
name=
graphicname=SPR_chr03
functionset=SignBoard
}

# gun
{
templatename=npcgen_gun
makeatnobody=1
makeatnosee=1
name=
graphicname=SPR_chr33
functionset=Gun
loopfunctime=3000
}
# box
{
templatename=npcgen_box
makeatnobody=1
makeatnosee=1
name=
graphicname=SPR_chr33
loopfunctime=2000
functionset=Box
}
# movewall
{
templatename=npcgen_movewall
makeatnobody=1
makeatnosee=1
graphicname=SPR_chr33
loopfunctime=2000
functionset=Movewall
hp=1000
mp=-1000
tough=0
}
# stepsw
{
templatename=npcgen_stepsw
makeatnobody=1
makeatnosee=1
graphicname=SPR_chr33
loopfunctime=2000
functionset=StepSwitch
}

# ship
{
templatename=npcgen_ship
makeatnobody=1
makeatnosee=1
graphicname=0
loopfunctime=2000
functionset=Ship
}

#temple
{
templatename=npcgen_temple
makeatnobody=1
makeatnosee=1
graphicname=0
functionset=Temple
}

# roomadmin
{
templatename=npcgen_roomadmin
makeatnobody=1
makeatnosee=1
graphicname=0
loopfunctime=2000
functionset=RoomAdmin
}

# doorman
{
templatename=npcgen_doorman
makeatnobody=1
makeatnosee=1
graphicname=0
functionset=Doorman
}
# class01
{
templatename=npcgen_class01
makeatnobody=1
makeatnosee=1
graphicname=0
functionset=Class01
}
# keyman
{
templatename=npcgen_keyman
makeatnobody=1
makeatnosee=1
graphicname=0
functionset=Keyman
}
# mugon
{
templatename=npcgen_mugon
makeatnobody=1
makeatnosee=1
graphicname=0
functionset=
}

