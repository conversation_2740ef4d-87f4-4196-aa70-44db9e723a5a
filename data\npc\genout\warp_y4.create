NPCCREATE
# generated by npcgen.perl which is made by ringo
#
#
# infile : data/npc/seimu/warp_y4.gen
#line 23
{
floorid=6000
borncorner=55,70,55,70
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6009|10|25
}
# between
{
floorid=6009
borncorner=10,25,10,25
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6000|55|70
}
#line 24
{
floorid=6000
borncorner=55,71,55,71
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6009|10|26
}
# between
{
floorid=6009
borncorner=10,26,10,26
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6000|55|71
}
#line 26
{
floorid=6000
borncorner=52,58,52,58
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6004|15|21
}
# between
{
floorid=6004
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6000|52|58
}
#line 27
{
floorid=6000
borncorner=53,58,53,58
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6004|16|21
}
# between
{
floorid=6004
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6000|53|58
}
#line 29
{
floorid=6005
borncorner=15,21,15,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6000|43|67
}
#line 30
{
floorid=6000
borncorner=44,67,44,67
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6005|16|21
}
# between
{
floorid=6005
borncorner=16,21,16,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6000|44|67
}
#line 32
{
floorid=6000
borncorner=61,58,61,58
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6006|10|20
}
# between
{
floorid=6006
borncorner=10,20,10,20
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6000|61|58
}
#line 33
{
floorid=6000
borncorner=61,59,61,59
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6006|10|21
}
# between
{
floorid=6006
borncorner=10,21,10,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|6000|61|59
}
