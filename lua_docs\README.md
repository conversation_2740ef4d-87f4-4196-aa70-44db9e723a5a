# Stone Age Server Lua 開發文檔

這是 Stone Age 服務器 mylua 系統的完整開發文檔庫，包含通用的開發指南和具體項目的實現文檔。

## 📁 文檔結構

```
lua_docs/
├── README.md                          # 本文檔 (總索引)
├── mylua_reference/                   # mylua 系統參考文檔
│   ├── README.md                      # mylua 參考索引
│   ├── coding_reference.md            # 編程參考手冊
│   ├── string_reference.md            # 字符串常數參考
│   ├── best_practices.md              # 開發最佳實踐
│   ├── common_errors.md               # 常見錯誤避免
│   └── testing_and_debugging.md       # 測試和調試指南
└── projects/                          # 具體項目文檔
    └── family_system/                 # 家族建設系統
        ├── README.md                  # 項目概述
        ├── system_overview.md         # 系統功能說明
        ├── deployment_guide.md        # 部署指南
        ├── database_schema.md         # 數據庫設計
        ├── troubleshooting.md         # 故障排除
        └── changelog.md               # 更新日誌
```

## 🎯 文檔分類

### 📚 通用開發文檔 (mylua_reference/)
適用於所有 Stone Age Lua 項目的通用知識：
- **編程參考**: mylua 函數、變數、常數的完整參考
- **字符串參考**: 所有可用的中文字符串常數和對應的 C 定義
- **最佳實踐**: 開發習慣、源碼檢查流程、編碼規範
- **常見錯誤**: 開發中容易犯的錯誤和解決方案
- **測試調試**: 測試和調試的方法與工具

### 🏗️ 項目文檔 (projects/)
具體項目的實現文檔：
- **family_system/**: 家族建設系統的完整文檔
- **未來可擴展**: PVP 系統、商店系統、活動系統等

## 🚀 快速導航

### 🔰 新手開發者
1. 先閱讀 [mylua 系統參考](./mylua_reference/README.md)
2. 學習 [編程參考](./mylua_reference/coding_reference.md)
3. 查看 [字符串參考](./mylua_reference/string_reference.md) 了解可用的常數
4. 掌握 [最佳實踐](./mylua_reference/best_practices.md)
5. 查看 [常見錯誤](./mylua_reference/common_errors.md)

### 👨‍💻 經驗開發者
1. 使用 [編程參考](./mylua_reference/coding_reference.md) 作為快速查詢手冊
2. 使用 [字符串參考](./mylua_reference/string_reference.md) 查詢正確的字符串常數
3. 參考 [測試調試指南](./mylua_reference/testing_and_debugging.md) 了解調試方法
4. 查看具體項目文檔了解實現細節

### 🤖 AI 助手
1. 使用 [mylua 參考文檔](./mylua_reference/) 確認函數正確用法
2. 遵循 [最佳實踐](./mylua_reference/best_practices.md) 中的檢查流程
3. 參考 [項目文檔](./projects/) 了解具體實現模式
4. 查看 [常見錯誤](./mylua_reference/common_errors.md) 避免重複問題

## 📋 當前項目狀態

### ✅ 已完成項目
- **[家族建設系統](./projects/family_system/)** - 完整的家族建設和倉庫系統
  - 狀態: 可部署使用
  - 版本: 1.4
  - 特色: NPC 創建、窗口處理、權限控制

### 🚧 計劃中項目
- PVP 競技系統
- 商店交易系統
- 活動管理系統
- 公會戰爭系統

## 🔍 核心開發原則

### 1. 源碼檢查優先
```bash
# 每次使用 mylua 函數前都要檢查源碼
grep -r "function_name" mylua/
grep -r "變數名" mylua/charbase.c
```

### 2. 參考現有實現
```lua
-- 學習 pool.lua, donate.lua 等現有系統腳本
-- 遵循既定的實現模式
```

### 3. 記錄檢查結果
```lua
-- ✅ 已檢查 mylua/lssprotobase.c 確認正確用法
lssproto.windows(talkerindex, 1103, 8, 0, char.getWorkInt(npcindex, "對象"), data)
```

### 4. 使用正確編碼
```lua
-- 使用 Big5 編碼確保中文顯示正常
-- 文件放置在 data/ablua/npc/system/ 目錄
```

## 📚 學習路徑

### 階段 1: 基礎知識
- 了解 mylua 系統架構
- 學習基本的函數調用規範
- 掌握正確的編碼和文件組織

### 階段 2: 實踐開發
- 學習 NPC 創建和事件處理
- 掌握窗口系統和數據庫操作
- 實現簡單的功能模組

### 階段 3: 高級應用
- 設計複雜的系統架構
- 實現跨模組的功能整合
- 優化性能和用戶體驗

## 🛠️ 開發工具

### 源碼檢查工具
```bash
# 函數檢查
grep -r "function_name" mylua/

# 變數檢查
grep -r "變數名" mylua/charbase.c

# 常數檢查
grep -r "CONSTANT_NAME" include/
```

### 參考資源
- `mylua/*.c` - C 函數實現
- `include/*.h` - 常數和枚舉定義
- `data/ablua/npc/system/*.lua` - 現有系統腳本

## 🎯 貢獻指南

### 添加新項目
1. 在 `projects/` 下創建新的項目資料夾
2. 參考 `family_system/` 的文檔結構
3. 更新本 README 的項目列表

### 改進通用文檔
1. 在 `mylua_reference/` 中添加新的參考資料
2. 更新相關的索引和導航
3. 確保所有檢查都有源碼依據

### 文檔規範
- 使用 Big5 編碼
- 提供完整的源碼檢查記錄
- 包含實際的使用範例
- 添加適當的錯誤處理說明

---

**版本**: 1.0  
**作者**: Augment Agent  
**最後更新**: 2025-06-18  
**說明**: 重新組織的文檔結構，通用 Lua 知識與具體項目分離
