NPCCREATE
# generated by npcgen.perl which is made by ringo
#
#
# infile : data/npc/sainasu/dungeon/11101d.gen
#line 5
{
floorid=11101
borncorner=33,28,33,28
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|100|442|127
}
#line 7
{
floorid=11101
borncorner=64,5,64,5
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11102|46|7
}
# between
{
floorid=11102
borncorner=46,7,46,7
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11101|64|5
}
#line 9
{
floorid=11102
borncorner=9,44,9,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11103|11|37
}
# between
{
floorid=11103
borncorner=11,37,11,37
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11102|9|44
}
#line 11
{
floorid=11103
borncorner=7,3,7,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11104|7|3
}
# between
{
floorid=11104
borncorner=7,3,7,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11103|7|3
}
#line 13
{
floorid=11104
borncorner=45,43,45,43
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11105|46|44
}
# between
{
floorid=11105
borncorner=46,44,46,44
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11104|45|43
}
#line 15
{
floorid=11105
borncorner=24,3,24,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11106|24|3
}
# between
{
floorid=11106
borncorner=24,3,24,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11105|24|3
}
#line 17
{
floorid=11106
borncorner=18,39,18,39
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11107|18|41
}
# between
{
floorid=11107
borncorner=18,41,18,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|11106|18|39
}
#line 20
{
floorid=11107
borncorner=32,7,32,7
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|100|466|128
}
