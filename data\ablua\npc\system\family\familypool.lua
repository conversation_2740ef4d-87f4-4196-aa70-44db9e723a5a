--=============================================
-- 家族倉庫系統 (Family Pool System)
-- 功能: 家族成員共享道具和寵物倉庫
-- 作者: Augment Agent
-- 版本: 1.0
-- 參考: pool.lua
--=============================================

-- 全域配置表
local family_pool_config = {}

-- 配置函數
function data()
    -- 家族倉庫設定
    family_pool_config.MAX_FAMILY_ITEM_SLOTS = 30    -- 家族道具倉庫最大格數
    family_pool_config.MAX_FAMILY_PET_SLOTS = 30     -- 家族寵物倉庫最大格數
    
    -- 禁止存放道具清單(參考pool.lua)
    family_pool_config.nosaveitemlist = {
        18543,18544,30027,30028,30029,30030,24136,25770,30458,30470,30793,30794,30795,30796,30796,30797,30798,30799,31089,
        35188,35189,35190,35191,35192,
        31603,31604,31605,
        31686,31687,31681,31682,31683,31684,31685,
        31751,31752,31753,
        31849,31850,31851,31852,31853,31854,
        31925,31926,31927,31928,
        32005,32006,32007,32008,32009,32010,32011,
        35193,35194,35195,35196,35197,35198,35199,35201,
        32273,32274,32275,32276,32277,32278,32279,
        35202,35203,35204,35205,35207,
        35208,35209,35210,35211,35212,35213,35214,35215,35216,
        32994
    }
end

-- 檢查玩家家族狀態 (已檢查 mylua/charbase.c 和 include/char_base.h 確認正確用法)
function checkPlayerFamilyStatus(talkerindex)
    local family_status = char.getInt(talkerindex, "家族地位")  -- CHAR_FMLEADERFLAG
    local family_index = char.getInt(talkerindex, "家族索引")   -- CHAR_FMINDEX

    -- family_status 對應 FMMEMBER 常數:
    -- FMMEMBER_NONE = -1, FMMEMBER_MEMBER = 1, FMMEMBER_APPLY = 2,
    -- FMMEMBER_LEADER = 3, FMMEMBER_ELDER = 4
    return family_status, family_index
end

-- 檢查家族是否已有建設
function checkFamilyConstruction(family_index)
    local token = "SELECT construction_level FROM family_constructions WHERE family_index=" .. family_index
    local ret = sasql.query(token, 1)

    if ret > 0 then
        sasql.fetch_row(1)
        if sasql.data(1) ~= nil then
            return other.atoi(sasql.data(1))
        end
    end
    return 0
end

-- 檢查道具是否禁止存放
function isInNoSaveList(itemid)
    for i = 1, table.getn(family_pool_config.nosaveitemlist) do
        if family_pool_config.nosaveitemlist[i] == itemid then
            return 1
        end
    end
    return 0
end

-- 獲取家族道具倉庫數量
function getFamilyItemCount(family_index)
    local token = "SELECT COUNT(*) FROM family_warehouse WHERE family_index=" .. family_index .. " AND item_id IS NOT NULL"
    local ret = sasql.query(token, 1)

    if ret > 0 then
        sasql.fetch_row(1)
        if sasql.data(1) ~= nil then
            return other.atoi(sasql.data(1))
        end
    end
    return 0
end

-- 獲取家族寵物倉庫數量
function getFamilyPetCount(family_index)
    local token = "SELECT COUNT(*) FROM family_warehouse_pets WHERE family_index=" .. family_index
    local ret = sasql.query(token, 1)

    if ret > 0 then
        sasql.fetch_row(1)
        if sasql.data(1) ~= nil then
            return other.atoi(sasql.data(1))
        end
    end
    return 0
end

-- 存放道具到家族倉庫
function saveFamilyItem(talkerindex, haveitemindex)
    local family_status, family_index = checkPlayerFamilyStatus(talkerindex)
    if family_status == -1 then  -- FMMEMBER_NONE
        char.TalkToCli(talkerindex, -1, "[系統]: 你還沒有加入家族", "紅色")
        return false
    end

    -- 檢查家族是否已建設
    if checkFamilyConstruction(family_index) == 0 then
        char.TalkToCli(talkerindex, -1, "[系統]: 家族尚未建設，無法使用倉庫", "紅色")
        return false
    end
    
    local itemindex = char.getItemIndex(talkerindex, haveitemindex)
    if itemindex < 0 then
        char.TalkToCli(talkerindex, -1, "[系統]: 找不到道具", "紅色")
        return false
    end
    
    -- 檢查是否禁止存放
    if isInNoSaveList(item.getInt(itemindex, "序號")) > 0 then
        char.TalkToCli(talkerindex, -1, "[系統]: 該道具無法存放", "紅色")
        return false
    end
    
    -- 檢查倉庫是否已滿
    local current_count = getFamilyItemCount(family_index)
    if current_count >= family_pool_config.MAX_FAMILY_ITEM_SLOTS then
        char.TalkToCli(talkerindex, -1, "[系統]: 家族道具倉庫已滿", "紅色")
        return false
    end

    -- 尋找空的格子
    local slot_id = 1
    for i = 1, family_pool_config.MAX_FAMILY_ITEM_SLOTS do
        local check_token = "SELECT slot_id FROM family_warehouse WHERE family_index=" .. family_index .. " AND slot_id=" .. i
        local check_ret = sasql.query(check_token, 1)
        if check_ret == 0 then
            slot_id = i
            break
        end
    end

    -- 存放道具資料
    local item_data = string.gsub(char.charSaveItemToString(itemindex), "\\", "\\\\")
    local item_id = item.getInt(itemindex, "序號")
    local item_count = item.getInt(itemindex, "堆疊")

    local insert_token = "INSERT INTO family_warehouse (family_index, slot_id, item_id, item_count, item_data) VALUES ("
        .. family_index .. ", " .. slot_id .. ", " .. item_id .. ", " .. item_count .. ", '" .. item_data .. "')"
    
    local ret = sasql.query(insert_token, 0)
    if ret >= 0 then
        -- 刪除玩家身上道具
        local pilenum = item.getInt(itemindex, "堆疊")
        char.setWorkInt(talkerindex, "NPC變數9", 1)
        if pilenum > 1 then
            char.DelItemNum(talkerindex, haveitemindex, pilenum)
        else
            char.DelItem(talkerindex, haveitemindex)
        end
        char.setWorkInt(talkerindex, "NPC變數9", 0)
        char.charSaveFromConnect(talkerindex)
        
        char.TalkToCli(talkerindex, -1, "[系統]: " .. item.getChar(itemindex, "名稱") .. " 已存入家族倉庫", "綠色")
        return true
    else
        char.TalkToCli(talkerindex, -1, "[系統]: 存放失敗，請重試", "紅色")
        return false
    end
end

-- 從家族倉庫取出道具
function drawFamilyItem(talkerindex, slot_id)
    local family_status, family_index = checkPlayerFamilyStatus(talkerindex)
    if family_status == -1 then
        char.TalkToCli(talkerindex, -1, "[系統]: 你還沒有加入家族", "紅色")
        return false
    end
    
    -- 檢查玩家背包是否有空位
    local empty_slot = -1
    for i = 9, 23 do
        local itemindex = char.getItemIndex(talkerindex, i)
        if itemindex < 0 then
            empty_slot = i
            break
        end
    end
    
    if empty_slot == -1 then
        char.TalkToCli(talkerindex, -1, "[系統]: 背包已滿", "紅色")
        return false
    end
    
    -- 獲取道具資料
    local token = "SELECT item_data, item_id FROM family_warehouse WHERE family_index=" .. family_index .. " AND slot_id=" .. slot_id
    local ret = sasql.query(token, 1)

    if ret > 0 then
        sasql.fetch_row(1)
        local item_data = sasql.data(1)
        local item_id = sasql.data(2)

        if item_data ~= nil and item_data ~= "" then
            -- 刪除倉庫中的道具
            local delete_token = "DELETE FROM family_warehouse WHERE family_index=" .. family_index .. " AND slot_id=" .. slot_id
            sasql.query(delete_token, 0)
            
            -- 給玩家道具
            local itemindex = char.charLoadItemToString(talkerindex, item_data, "家族倉庫取出")
            char.TalkToCli(talkerindex, -1, "[系統]: " .. item.getChar(itemindex, "名稱") .. " 已取出", "綠色")
            char.charSaveFromConnect(talkerindex)
            return true
        end
    end
    
    char.TalkToCli(talkerindex, -1, "[系統]: 該位置沒有道具", "紅色")
    return false
end

-- 存放寵物到家族倉庫
function saveFamilyPet(talkerindex, petslot)
    local family_status, family_index = checkPlayerFamilyStatus(talkerindex)
    if family_status == -1 then
        char.TalkToCli(talkerindex, -1, "[系統]: 你還沒有加入家族", "紅色")
        return false
    end

    -- 檢查家族是否已建設
    if checkFamilyConstruction(family_index) == 0 then
        char.TalkToCli(talkerindex, -1, "[系統]: 家族尚未建設，無法使用倉庫", "紅色")
        return false
    end
    
    local petindex = char.getCharPet(talkerindex, petslot)
    if char.check(petindex) ~= 1 then
        char.TalkToCli(talkerindex, -1, "[系統]: 找不到寵物", "紅色")
        return false
    end
    
    -- 檢查是否出戰中寵物
    if char.getInt(talkerindex, "戰寵") == petslot then
        char.TalkToCli(talkerindex, -1, "[系統]: 請先收回戰寵", "紅色")
        return false
    end
    
    -- 檢查倉庫是否已滿
    local current_count = getFamilyPetCount(family_index)
    if current_count >= family_pool_config.MAX_FAMILY_PET_SLOTS then
        char.TalkToCli(talkerindex, -1, "[系統]: 家族寵物倉庫已滿", "紅色")
        return false
    end

    -- 尋找空的格子
    local slot_id = 1
    for i = 1, family_pool_config.MAX_FAMILY_PET_SLOTS do
        local check_token = "SELECT slot_id FROM family_warehouse_pets WHERE family_index=" .. family_index .. " AND slot_id=" .. i
        local check_ret = sasql.query(check_token, 1)
        if check_ret == 0 then
            slot_id = i
            break
        end
    end
    
    -- 清理寵物名稱中的特殊字符
    if string.find(char.getChar(petindex, "寵物名"), ':', 1, true) or
       string.find(char.getChar(petindex, "寵物名"), '|', 1, true) or
       string.find(char.getChar(petindex, "寵物名"), '&', 1, true) then
        local ownt = char.getChar(petindex, "寵物名")
        ownt = string.gsub(ownt, ":", "")
        ownt = string.gsub(ownt, "|", "")
        ownt = string.gsub(ownt, "&", "")
        char.setChar(petindex, "寵物名", ownt)
    end
    
    -- 存放寵物資料
    local pet_data = char.charSavePetToString(petindex)
    local insert_token = "INSERT INTO family_warehouse_pets (family_index, slot_id, pet_data) VALUES ("
        .. family_index .. ", " .. slot_id .. ", '" .. pet_data .. "')"
    
    local ret = sasql.query(insert_token, 0)
    if ret >= 0 then
        -- 刪除玩家身上寵物
        char.setWorkInt(talkerindex, "NPC變數9", 1)
        char.DelPet(talkerindex, petindex)
        char.setWorkInt(talkerindex, "NPC變數9", 0)
        char.charSaveFromConnect(talkerindex)
        
        char.TalkToCli(talkerindex, -1, "[系統]: " .. char.getChar(petindex, "寵物名") .. " 已存入家族倉庫", "綠色")
        return true
    else
        char.TalkToCli(talkerindex, -1, "[系統]: 存放失敗，請重試", "紅色")
        return false
    end
end

-- 從家族倉庫取出寵物
function drawFamilyPet(talkerindex, slot_id)
    local family_status, family_index = checkPlayerFamilyStatus(talkerindex)
    if family_status == -1 then
        char.TalkToCli(talkerindex, -1, "[系統]: 你還沒有加入家族", "紅色")
        return false
    end

    -- 檢查玩家是否有空的寵物位置
    local empty_slot = char.findEmptyPetBox(talkerindex)
    if empty_slot < 1 then
        char.TalkToCli(talkerindex, -1, "[系統]: 寵物欄已滿", "紅色")
        return false
    end

    -- 獲取寵物資料
    local token = "SELECT pet_data FROM family_warehouse_pets WHERE family_index=" .. family_index .. " AND slot_id=" .. slot_id
    local ret = sasql.query(token, 1)

    if ret > 0 then
        sasql.fetch_row(1)
        local pet_data = sasql.data(1)

        if pet_data ~= nil and pet_data ~= "" then
            -- 刪除倉庫中的寵物
            local delete_token = "DELETE FROM family_warehouse_pets WHERE family_index=" .. family_index .. " AND slot_id=" .. slot_id
            sasql.query(delete_token, 0)

            -- 給玩家寵物
            local petindex = char.charLoadPetToString(talkerindex, pet_data, "家族倉庫取出")
            char.TalkToCli(talkerindex, -1, "[系統]: " .. char.getChar(petindex, "寵物名") .. " 已取出", "綠色")
            char.charSaveFromConnect(talkerindex)
            return true
        end
    end

    char.TalkToCli(talkerindex, -1, "[系統]: 該位置沒有寵物", "紅色")
    return false
end

-- 生成家族道具倉庫資料清單
function genFamilyItemDataList(talkerindex)
    local family_status, family_index = checkPlayerFamilyStatus(talkerindex)
    if family_status == -1 then
        return ""
    end

    local ret = ""
    local token = "SELECT slot_id, item_id, item_count, item_data FROM family_warehouse WHERE family_index=" .. family_index .. " AND item_id IS NOT NULL ORDER BY slot_id"
    local query_ret = sasql.query(token, 1)

    if query_ret > 0 then
        for i = 1, query_ret do
            sasql.fetch_row(i)
            local slot_id = sasql.data(1)
            local item_id = other.atoi(sasql.data(2))
            local item_count = other.atoi(sasql.data(3))
            local item_data = sasql.data(4)

            -- 生成道具字符串(參考pool.lua 的genItemString)
            local itemimg = item.getgraNoFromITEMtabl(item_id)
            local itemname = item.getNameFromNumber(item_id)
            local iteminfo = item.getItemInfoFromNumber(item_id)

            local item_string = string.format("%s,%d,%s,%d", itemname, itemimg, iteminfo, item_count)
            ret = ret .. item_string .. "," .. slot_id .. "|"
        end
    end

    ret = family_pool_config.MAX_FAMILY_ITEM_SLOTS .. "&0&" .. ret
    return ret
end

-- 生成家族寵物倉庫資料清單
function genFamilyPetDataList(talkerindex)
    local family_status, family_index = checkPlayerFamilyStatus(talkerindex)
    if family_status == -1 then
        return ""
    end

    local ret = ""
    local token = "SELECT slot_id, pet_data FROM family_warehouse_pets WHERE family_index=" .. family_index .. " ORDER BY slot_id"
    local query_ret = sasql.query(token, 1)

    if query_ret > 0 then
        for i = 1, query_ret do
            sasql.fetch_row(i)
            local slot_id = sasql.data(1)
            local pet_data = sasql.data(2)

            -- 生成寵物字符串(參考pool.lua 的genPetString)
            local pet_string = genFamilyPetString(pet_data)
            ret = ret .. pet_string .. "," .. slot_id .. ":"
        end
    end

    ret = family_pool_config.MAX_FAMILY_PET_SLOTS .. "&0&" .. ret
    return ret
end

-- 生成寵物字符串(參考pool.lua)
function genFamilyPetString(petdata)
    local name = getAttribute(petdata, "ownt")
    if name == "" then
        name = getAttribute(petdata, "name")
    end
    local petid = tonumber(getAttribute(petdata, "dmswc"))
    local tempno = enemytemp.getEnemyTempArrayFromTempNo(petid)

    local vi = tonumber(getAttribute(petdata, "vi"))
    local str = tonumber(getAttribute(petdata, "str"))
    local tou = tonumber(getAttribute(petdata, "tou"))
    local dx = tonumber(getAttribute(petdata, "dx"))
    local lv = tonumber(getAttribute(petdata, "lv"))

    local earth = other.atoi(getAttribute(petdata, "aea"))
    local water = other.atoi(getAttribute(petdata, "awa"))
    local fire = other.atoi(getAttribute(petdata, "afi"))
    local wind = other.atoi(getAttribute(petdata, "awi"))

    local hp, attr, def, dex = get4V(vi, str, tou, dx)

    return string.format("%s,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d",
        name,
        enemytemp.getInt(tempno, "圖號"),
        earth,
        water,
        fire,
        wind,
        hp,
        attr,
        def,
        dex,
        lv
    )
end

-- 解析屬性(參考pool.lua)
function getAttribute(data, tag)
    local start = string.find(data, tag)
    if start == nil then
        return ""
    end

    start = start + string.len(tag) + 1
    local en = string.find(string.sub(data, start), "|")
    if tag == "name" then
        en = string.find(string.sub(data, start), "|ownt")
    elseif tag == "ownt" then
        en = string.find(string.sub(data, start), "|arg")
    end
    if en == nil then
        return ""
    end

    return string.sub(data, start, start + en - 2)
end

-- 計算寵物四維 (參考pool.lua)
function get4V(TM_Vi, TM_St, TM_To, TM_Dx)
    local TM_PetHP = math.floor((TM_Vi * 4 + TM_St + TM_To + TM_Dx) * 0.01)
    local TM_PetStr = math.floor(TM_St * 0.01 + TM_To * 0.01 * 0.1 + TM_Vi * 0.01 * 0.1 + TM_Dx * 0.01 * 0.05)
    local TM_PetTough = math.floor(TM_To * 0.01 + TM_St * 0.01 * 0.1 + TM_Vi * 0.01 * 0.1 + TM_Dx * 0.01 * 0.05)
    local TM_PetDex = math.floor(TM_Dx * 0.01)
    return TM_PetHP, TM_PetStr, TM_PetTough, TM_PetDex
end

-- 開啟家族道具倉庫介面 (已檢查 mylua/lssprotobase.c 確認正確用法)
function openFamilyItemStorage(talkerindex, npcindex)
    local family_status, family_index = checkPlayerFamilyStatus(talkerindex)
    if family_status == -1 then
        char.TalkToCli(talkerindex, -1, "[系統]: 你還沒有加入家族", "紅色")
        return
    end

    -- 檢查家族是否已建設
    if checkFamilyConstruction(family_index) == 0 then
        char.TalkToCli(talkerindex, -1, "[系統]: 家族尚未建設，無法使用倉庫", "紅色")
        return
    end

    if char.getWorkInt(talkerindex, "NPC變數10") < other.time() then
        -- 使用標準倉庫窗口 (參考 pool.lua 的正確用法)
        -- 參數: (talkerindex, windowtype, buttontype, seqno, objindex, data)
        lssproto.windows(talkerindex, 1103, 8, 0, char.getWorkInt(npcindex, "對象"), genFamilyItemDataList(talkerindex))
        char.setWorkInt(talkerindex, "NPC變數10", other.time())
    end
end

-- 開啟家族寵物倉庫介面 (已檢查 mylua/lssprotobase.c 確認正確用法)
function openFamilyPetStorage(talkerindex, npcindex)
    local family_status, family_index = checkPlayerFamilyStatus(talkerindex)
    if family_status == -1 then
        char.TalkToCli(talkerindex, -1, "[系統]: 你還沒有加入家族", "紅色")
        return
    end

    -- 檢查家族是否已建設
    if checkFamilyConstruction(family_index) == 0 then
        char.TalkToCli(talkerindex, -1, "[系統]: 家族尚未建設，無法使用倉庫", "紅色")
        return
    end

    if char.getWorkInt(talkerindex, "NPC變數10") < other.time() then
        -- 使用標準寵物倉庫窗口 (參考 pool.lua 的正確用法)
        -- 參數: (talkerindex, windowtype, buttontype, seqno, objindex, data)
        lssproto.windows(talkerindex, 1102, 8, 0, char.getWorkInt(npcindex, "對象"), genFamilyPetDataList(talkerindex))
        char.setWorkInt(talkerindex, "NPC變數10", other.time())
    end
end

-- 注意: 此文件不創建 NPC，因此不需要 WindowTalked 函數
-- 窗口事件由 family_system.lua 中的 NPC 處理

-- 主函數
function main()
    data()
end
