NPCCREATE
# generated by npcgen.perl which is made by ringo
#
#
# infile : data/npc/jaruga/dungeon/20801d.gen
#line 5
{
floorid=200
borncorner=433,742,433,742
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|20801|33|69
}
# between
{
floorid=20801
borncorner=33,69,33,69
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|200|433|742
}
#line 6
{
floorid=20801
borncorner=34,69,34,69
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|200|433|742
}
#line 9
{
floorid=20801
borncorner=12,4,12,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|20802|10|3
}
# between
{
floorid=20802
borncorner=10,3,10,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|20801|12|4
}
#line 11
{
floorid=20802
borncorner=5,40,5,40
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|20803|21|7
}
# between
{
floorid=20803
borncorner=21,7,21,7
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|20802|5|40
}
#line 13
{
floorid=20803
borncorner=37,4,37,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|20804|33|4
}
# between
{
floorid=20804
borncorner=33,4,33,4
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|20803|37|4
}
#line 15
{
floorid=20804
borncorner=11,37,11,37
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|20805|36|3
}
# between
{
floorid=20805
borncorner=36,3,36,3
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|20804|11|37
}
#line 17
{
floorid=20805
borncorner=44,21,44,21
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|20806|46|32
}
# between
{
floorid=20806
borncorner=46,32,46,32
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|20805|44|21
}
#line 19
{
floorid=20806
borncorner=46,12,46,12
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|20807|5|38
}
# between
{
floorid=20807
borncorner=5,38,5,38
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|20806|46|12
}
#line 23
{
floorid=20807
borncorner=45,41,45,41
time=0
ignoreinvincible=1
createnum=1
enemy=npcgen_warp|200|452|745
}
