# Stone Age Server mylua 編程參考

## 概述

這個文檔記錄了 Stone Age 服務器 mylua 系統的正確使用方式，包括函數調用、變數名稱、編碼規範等重要信息。

## 🚨 重要原則

### 1. 源碼檢查習慣
**每次使用 mylua 函數或變數前都必須檢查源碼**

```bash
# 檢查函數定義
grep -r "function_name" mylua/

# 檢查變數名稱
grep -r "變數名" mylua/charbase.c

# 檢查枚舉常數
grep -r "ENUM_NAME" include/
```

### 2. 文件位置規範
```lua
-- ✅ 正確的文件位置
data/ablua/npc/system/script_name.lua

-- ❌ 錯誤的位置
./script_name.lua
```

### 3. 編碼規範
```lua
-- ✅ 使用 Big5 編碼確保中文顯示正常
-- 參考現有的 pool.lua 等系統腳本

-- ❌ 使用 UTF-8 會導致中文亂碼
```

### 4. 工具函數引入原則
```lua
-- ✅ 只引入實際使用的工具函數
dofile("./data/ablua/func/utils.lua")  -- 只有在使用 utils 函數時才引入

-- ✅ 不使用時明確註釋說明
-- 注意：不需要引入 utils.lua，因為本腳本沒有使用到其中的函數

-- ❌ 盲目引入未使用的工具函數
dofile("./data/ablua/func/utils.lua")  -- 但腳本中沒有使用任何 utils.* 函數
```

## 🔍 mylua 函數使用指南

### 1. NPC 創建和管理
```lua
-- ✅ 正確的 NPC 創建方式 (已檢查 mylua/npcbase.c)
local npcindex = npc.CreateNpc(name, metamo, floor, x, y, dir)
if npcindex >= 0 then
    -- 設置對話函數指針
    char.setFunctionPointer(npcindex, "對話事件", "Talked", "")
    -- 設置窗口對話函數指針
    char.setFunctionPointer(npcindex, "窗口事件", "WindowTalked", "")
end
```

### 2. 跨文件函數調用
```lua
-- ✅ 正確用法 (已檢查 mylua/otherbase.c)
other.CallFunction("functionName", "./filename.lua", {param1, param2})

-- 參數說明：
-- functionName: 要調用的函數名稱 (字符串)
-- filename: 目標文件路徑 (字符串)
-- args_table: 參數表 (只能包含整數)

-- ❌ 錯誤用法
other.callfunction("./file.lua", "func", param)  -- 錯誤的函數名和參數順序
dofile("./file.lua"); func(param)                -- 不安全的直接調用
```

### 3. 家族系統變數 (已檢查 mylua/charbase.c 和 include/char_base.h)
```lua
-- ✅ 正確的家族變數名稱
local family_status = char.getInt(talkerindex, "家族地位")  -- CHAR_FMLEADERFLAG
local family_index = char.getInt(talkerindex, "家族索引")   -- CHAR_FMINDEX
local family_name = char.getChar(talkerindex, "家族")       -- CHAR_FMNAME

-- 家族地位常數 (FMMEMBER 枚舉):
-- FMMEMBER_NONE = -1     (沒有家族)
-- FMMEMBER_MEMBER = 1    (家族成員)
-- FMMEMBER_APPLY = 2     (申請入族)
-- FMMEMBER_LEADER = 3    (族長)
-- FMMEMBER_ELDER = 4     (長老)

-- ❌ 錯誤的變數名稱
char.getInt(talkerindex, "家族編號")  -- 不存在此變數
char.getInt(talkerindex, "家族ID")    -- 不存在此變數
```

### 4. 道具操作函數 (已檢查 mylua/charbase.c 和 mylua/npcbase.c)
```lua
-- ✅ 正確的道具檢查和消耗方式
local count = char.countitem(talkerindex, itemid)  -- 檢查道具數量
local result = npc.DelItemNum(talkerindex, itemid .. "," .. count)  -- 刪除道具

-- 參數說明：
-- char.countitem(talkerindex, itemid) - 返回道具總數量
-- npc.DelItemNum(talkerindex, "itemid,count") - 刪除指定數量道具，成功返回1

-- ❌ 錯誤的手動遍歷方式
for i = 8, 27 do
    local itemindex = char.getItemIndex(talkerindex, i)
    -- 手動計算和刪除道具 - 複雜且容易出錯
end
```

### 5. 窗口系統函數 (已檢查 mylua/lssprotobase.c 確認正確用法)
```lua
-- ✅ 正確的 lssproto.windows 用法 (參考 pool.lua)
lssproto.windows(talkerindex, windowtype, buttontype, seqno, objindex, data)

-- 參數說明：
-- talkerindex: 玩家索引
-- windowtype: 窗口類型 (1103=道具倉庫, 1102=寵物倉庫)
-- buttontype: 按鈕類型 (8=標準)
-- seqno: 序列號 (0=標準)
-- objindex: 對象索引 (使用 char.getWorkInt(npcindex, "對象"))
-- data: 數據字符串

-- ✅ 實際使用範例
lssproto.windows(talkerindex, 1103, 8, 0, char.getWorkInt(npcindex, "對象"), genItemDataList(talkerindex))

-- ❌ 錯誤用法
lssproto.windows(talkerindex, 1103, 8, 0, 999999, data)  -- 999999 是錯誤的對象索引
```

### 6. 顏色使用規範
```lua
-- ✅ 正確的顏色名稱
char.TalkToCli(talkerindex, -1, "成功訊息", "綠色")
char.TalkToCli(talkerindex, -1, "錯誤訊息", "紅色")
char.TalkToCli(talkerindex, -1, "警告訊息", "黃色")
char.TalkToCli(talkerindex, -1, "一般訊息", "白色")

-- ❌ 錯誤的顏色名稱
char.TalkToCli(talkerindex, -1, "訊息", "green")  -- 應該用中文
```

## 🏗️ 權限控制設計原則

### 1. 家族權限分層
```lua
-- ✅ 正確的權限控制設計
if family_status == 3 then  -- FMMEMBER_LEADER
    -- 族長專用功能：申請建設、升級建設、使用倉庫、查看資訊
elseif family_status >= 1 then  -- 有家族成員
    -- 族員可用功能：使用倉庫、查看資訊
else
    -- 無家族：只能查看條件
end

-- 權限設計原則：
-- 1. 建設功能只有族長可以申請和管理
-- 2. 倉庫功能所有家族成員都可以使用
-- 3. 資訊查看功能所有家族成員都可以使用
-- 4. 明確顯示權限限制，避免用戶困惑
```

### 2. 簡化的申請條件
```lua
-- ✅ 正確的申請條件檢查 (已簡化)
function checkApplicationRequirements(talkerindex)
    -- 只檢查必要條件：
    -- 1. 是否有家族
    -- 2. 是否為族長
    -- 3. 家族是否已有建設
    -- 4. 資源是否足夠
    
    -- 不需要檢查：
    -- - 隊伍成員數量 ❌
    -- - 隊長身份 ❌
end
```

## 🔧 NPC 系統設計

### 1. NPC 創建模式
```lua
-- ✅ 標準的 NPC 創建模式 (參考 pool.lua)
function CreateSystemNpc(name, metamo, floor, x, y, dir)
    local npcindex = npc.CreateNpc(name, metamo, floor, x, y, dir)
    if npcindex >= 0 then
        char.setFunctionPointer(npcindex, "對話事件", "Talked", "")
        char.setFunctionPointer(npcindex, "窗口事件", "WindowTalked", "")
        return npcindex
    end
    return -1
end

function main()
    data()
    -- 在 main 函數中創建 NPC
    CreateSystemNpc("NPC名稱", 外觀編號, 地圖, X, Y, 方向)
end
```

### 2. 事件處理函數
```lua
-- ✅ 標準的事件處理函數
function Talked(meindex, talkerindex, szMes, color)
    if npc.isFaceToFace(meindex, talkerindex) ~= 1 then return end
    -- 對話邏輯
end

function WindowTalked(meindex, talkerindex, seqno, select, data)
    if npc.isFaceToFace(meindex, talkerindex) ~= 1 then return end
    -- 窗口事件邏輯
end
```

## 📊 數據庫操作規範

### 1. 使用遊戲內建索引
```sql
-- ✅ 正確的數據庫設計
CREATE TABLE family_constructions (
    family_index INT NOT NULL UNIQUE,  -- 對應 char.getInt(talkerindex, "家族索引")
    -- 其他欄位...
);

-- ❌ 錯誤的自定義索引
CREATE TABLE family_constructions (
    family_number INT NOT NULL UNIQUE,  -- 不對應任何遊戲變數
    -- 其他欄位...
);
```

### 2. 查詢優化
```lua
-- ✅ 正確的查詢方式
local token = "SELECT construction_level FROM family_constructions WHERE family_index=" .. family_index
local ret = sasql.query(token, 1)

-- 建立適當索引
-- INDEX idx_family_index (family_index)
```

---

**版本**: 1.4  
**作者**: Augment Agent  
**最後更新**: 2025-06-18  
**說明**: mylua 系統的通用編程參考，適用於所有 Stone Age Lua 項目
