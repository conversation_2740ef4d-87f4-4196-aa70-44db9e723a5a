list ={}

function list:new()
	local l = {data = nil,next = nil}
	setmetatable(l,self)
	self.__index = self
	return l
end

function list:insert(value)
	local  l = self
	if not l.next then
		l./data = value
		l.next = {data = nil,next = nil}
	else
		while l.next do
			l = l.next
		end
		l./data = value
		l.next = {data = nil,next = nil}
	end
end

function list:lprint()
	local l = self
	while l.next do
		print(l./data)
		l = l.next
	end
end

function list:clear()
	while self.next do
		local  l = self.next
		self./data = nil
		self.next = nil
		self = l
	end
end

function list:find(value)
	local l = self
	local pos = 1
	while l.next do
		if l./data == value then
			return pos
		end
		pos = pos + 1
		l = l.next
	end
end

function list:erase(value)
	local l = self
	while l.next do
		local temp = l.next
		if temp./data == value then
			l.next = temp.next
			temp./data = nil
			temp.next = nil
			return
		end
	l = l.next
	end
end

function list:update(pos,value)
	local l = self
	local position = 1
	while position ~= pos do
		l = l.next
		position = position + 1
	end
	l./data = value
end

function list:size()
	return table.maxn(list)
end

l = list:new()
print(list:size())
for i = 1,10 do
	l:insert(i)
end
print(list:size())

l:erase(10)
print(list:size())

l:update(5,10)
l:lprint()
