# Stone Age Server 開發項目

這個工作區包含 Stone Age 服務器的 Lua 腳本開發項目和相關文檔。

## 📁 項目結構

```
gmsv/
├── README.md                          # 本文檔
├── data/ablua/npc/system/             # 實際的腳本文件
│   ├── family_system.lua              # 家族建設系統主腳本
│   └── familypool.lua                 # 家族倉庫函數庫
└── lua_docs/                          # 開發文檔
    ├── README.md                      # 文檔總索引
    ├── mylua_reference/               # mylua 系統通用參考
    │   ├── coding_reference.md        # 編程參考手冊
    │   ├── best_practices.md          # 開發最佳實踐
    │   └── common_errors.md           # 常見錯誤避免
    └── projects/                      # 具體項目文檔
        └── family_system/             # 家族建設系統
            ├── README.md              # 項目概述
            ├── system_overview.md     # 系統功能說明
            ├── deployment_guide.md    # 部署指南
            └── changelog.md           # 更新日誌
```

## 🚀 快速開始

### 📚 查看文檔
- **[Lua 開發文檔](./lua_docs/README.md)** - 完整的開發指南和參考
- **[家族建設系統](./lua_docs/projects/family_system/README.md)** - 具體項目文檔

### 💻 開發指南
1. 先閱讀 [mylua 編程參考](./lua_docs/mylua_reference/coding_reference.md)
2. 學習 [開發最佳實踐](./lua_docs/mylua_reference/best_practices.md)
3. 查看 [常見錯誤避免](./lua_docs/mylua_reference/common_errors.md)

### 🏗️ 項目部署
1. 查看 [家族系統概述](./lua_docs/projects/family_system/system_overview.md)
2. 按照 [部署指南](./lua_docs/projects/family_system/deployment_guide.md) 進行部署

## 🎯 當前項目

### ✅ 家族建設系統 (v1.5)
- **狀態**: 可部署使用
- **功能**: 家族建設申請、共享倉庫系統
- **特色**: 完整的 NPC 創建、窗口處理、權限控制
- **文檔**: [項目文檔](./lua_docs/projects/family_system/)

## 🔍 開發原則

### 核心習慣
1. **源碼檢查優先**: 每次使用 mylua 函數前都檢查源碼
2. **參考現有實現**: 學習 pool.lua 等現有系統腳本
3. **記錄檢查結果**: 在代碼中註釋檢查過的文件
4. **使用正確編碼**: Big5 編碼確保中文顯示正常

### 文檔組織
- **通用知識在外**: mylua 參考文檔適用於所有項目
- **具體項目在內**: 每個項目有獨立的文檔資料夾
- **清晰的導航**: 每個層級都有完整的索引

## 🛠️ 開發環境

### 必要工具
- Stone Age 服務器
- mylua 系統支持
- MySQL 數據庫
- 文本編輯器 (支持 Big5 編碼)

### 源碼檢查
```bash
# 檢查函數定義
grep -r "function_name" mylua/

# 檢查變數名稱
grep -r "變數名" mylua/charbase.c

# 檢查枚舉常數
grep -r "ENUM_NAME" include/
```

## 📋 未來計劃

### 短期目標
- 家族建設升級系統
- 更多倉庫功能 (分類、搜索)
- 家族成員權限細化

### 長期目標
- PVP 競技系統
- 商店交易系統
- 活動管理系統
- 公會戰爭系統

## 🤝 貢獻指南

### 添加新項目
1. 在 `lua_docs/projects/` 下創建新的項目資料夾
2. 參考 `family_system/` 的文檔結構
3. 更新相關的索引文檔

### 改進通用文檔
1. 在 `lua_docs/mylua_reference/` 中添加新的參考資料
2. 確保所有檢查都有源碼依據
3. 提供實際的使用範例

### 文檔規範
- 使用 Big5 編碼
- 提供完整的源碼檢查記錄
- 包含實際的使用範例
- 添加適當的錯誤處理說明

---

**維護者**: Augment Agent  
**最後更新**: 2025-06-18  
**文檔版本**: 1.5  
**說明**: 重新組織的文檔結構，通用 Lua 知識與具體項目分離
