# 家族建設系統 - 部署指南

## 修正說明

根據重要發現，我已經修正了以下關鍵問題：

1. **NPC 創建問題**: 添加了正確的 NPC 創建和函數指針設置
2. **窗口事件處理**: 整合了倉庫窗口事件到主系統中
3. **跨文件函數調用**: 使用正確的 `other.CallFunction` 方式
4. **家族變數名稱**: 使用 "家族索引" 而不是 "家族編號"
5. **道具操作函數**: 使用 `char.countitem` 和 `npc.DelItemNum`
6. **移除隊伍檢查**: 簡化申請條件，只檢查族長身份
7. **文件位置**: 移至正確的 `data/ablua/npc/system/` 目錄
8. **編碼問題**: 使用正確的 Big5 編碼
9. **窗口函數參數**: 修正 `lssproto.windows` 的對象索引參數

## 文件部署

### 1. 文件放置 (已修正)
```
data/ablua/npc/system/
├── family_system.lua      # 主要家族建設系統 (Big5編碼，包含NPC創建)
└── familypool.lua         # 家族倉庫函數庫 (Big5編碼，不創建NPC)
```

**重要修正**：
- family_system.lua 現在會創建 NPC 並設置正確的函數指針
- familypool.lua 作為函數庫，不創建 NPC
- 窗口事件由 family_system.lua 中的 NPC 統一處理

### 2. 載入順序
1. 先載入 `data/ablua/npc/system/familypool.lua` (函數庫)
2. 再載入 `data/ablua/npc/system/family_system.lua` (主系統，創建NPC)

### 3. NPC 創建 (已修正)
```lua
-- 在 family_system.lua 的 main() 函數中
function main()
    data()
    
    -- 創建家族建設管理員 NPC
    local family_npc = CreateFamilySystemNpc(
        "家族管理員",    -- NPC 名稱
        101813,          -- 外觀編號
        1000,            -- 地圖編號
        100,             -- X 座標
        100,             -- Y 座標
        6                -- 方向
    )
end

function CreateFamilySystemNpc(name, metamo, floor, x, y, dir)
    local npcindex = npc.CreateNpc(name, metamo, floor, x, y, dir)
    if npcindex >= 0 then
        -- 設置對話函數指針
        char.setFunctionPointer(npcindex, "對話事件", "Talked", "")
        -- 設置窗口對話函數指針
        char.setFunctionPointer(npcindex, "窗口事件", "WindowTalked", "")
        return npcindex
    end
    return -1
end
```

## 數據庫設置

### 1. 創建數據表
```sql
-- 家族建設表 (使用遊戲內建的家族索引)
CREATE TABLE family_constructions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    family_index INT NOT NULL UNIQUE,
    leader_name VARCHAR(50) NOT NULL,
    construction_level INT DEFAULT 1,
    create_time DATETIME NOT NULL,
    warehouse_slots INT DEFAULT 30,
    INDEX idx_family_index (family_index)
);

-- 家族倉庫表
CREATE TABLE family_warehouse (
    id INT AUTO_INCREMENT PRIMARY KEY,
    family_index INT NOT NULL,
    slot_id INT NOT NULL DEFAULT 0,
    item_id INT DEFAULT NULL,
    item_count INT DEFAULT 0,
    item_data TEXT DEFAULT NULL,
    slot_count INT DEFAULT 30,
    INDEX idx_family_slot (family_index, slot_id)
);

-- 家族倉庫寵物表
CREATE TABLE family_warehouse_pets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    family_index INT NOT NULL,
    slot_id INT NOT NULL,
    pet_data TEXT NOT NULL,
    INDEX idx_family_pet_slot (family_index, slot_id)
);
```

## 系統架構 (已修正)

### 1. NPC 和事件處理
```
family_system.lua (主系統)
├── 創建 NPC
├── 設置函數指針
├── 處理對話事件 (Talked)
├── 處理窗口事件 (WindowTalked)
└── 調用 familypool.lua 中的函數

familypool.lua (函數庫)
├── 不創建 NPC
├── 提供倉庫操作函數
├── 被 family_system.lua 調用
└── 處理數據庫操作
```

### 2. 跨文件函數調用 (已修正)
```lua
-- 在 family_system.lua 中調用 familypool.lua 的函數
other.CallFunction("openFamilyItemStorage", "./familypool.lua", {talkerindex, meindex})
other.CallFunction("openFamilyPetStorage", "./familypool.lua", {talkerindex, meindex})
other.CallFunction("saveFamilyItem", "./familypool.lua", {talkerindex, slot})
other.CallFunction("drawFamilyItem", "./familypool.lua", {talkerindex, slot})
```

### 2.1. 窗口函數修正 (已檢查 mylua/lssprotobase.c)
```lua
-- ❌ 錯誤方式 (已修正)
lssproto.windows(talkerindex, 1103, 8, 0, 999999, genFamilyItemDataList(talkerindex))

-- ✅ 正確方式 (參考 pool.lua)
lssproto.windows(talkerindex, 1103, 8, 0, char.getWorkInt(npcindex, "對象"), genFamilyItemDataList(talkerindex))

-- 參數說明：
-- talkerindex: 玩家索引
-- 1103/1102: 窗口類型 (道具倉庫/寵物倉庫)
-- 8: 按鈕類型
-- 0: 序列號
-- char.getWorkInt(npcindex, "對象"): 正確的對象索引
-- data: 數據字符串
```

### 3. 窗口事件處理 (已修正)
```lua
-- 在 family_system.lua 的 WindowTalked 函數中
function WindowTalked(meindex, talkerindex, seqno, select, data)
    -- 處理主選單事件
    if seqno == 0 then
        -- 主選單邏輯
    elseif seqno == 4 then
        -- 倉庫選單邏輯
    elseif seqno == 8 then
        -- 倉庫窗口事件 (1103道具倉庫, 1102寵物倉庫)
        handleWarehouseWindowEvent(talkerindex, select, data)
    end
end
```

## 測試流程

### 1. 家族建設測試 (已簡化)
1. 加入家族並成為族長
2. 準備足夠資源 (聲望、鑽石、珍珠、銅條、鐵條)
3. 與家族管理員對話
4. 選擇"申請家族建設"
5. 確認申請

### 2. 家族倉庫測試
1. 建設完成後，與 NPC 對話
2. 選擇"家族倉庫"
3. 選擇"道具倉庫"或"寵物倉庫"
4. 測試存入和取出功能

### 3. 權限測試
1. 測試族長權限 (申請建設、使用倉庫)
2. 測試族員權限 (使用倉庫、查看資訊)
3. 測試非家族成員權限 (只能查看條件)

## 常見問題解決

### Q: NPC 沒有出現？
A: 
1. 檢查 main() 函數是否正確調用
2. 檢查 NPC 座標是否正確
3. 檢查地圖編號是否存在
4. 查看服務器日誌確認 NPC 創建狀態

### Q: 對話沒有反應？
A: 
1. 檢查函數指針是否正確設置
2. 確認 Talked 和 WindowTalked 函數存在
3. 檢查 Big5 編碼是否正確

### Q: 倉庫窗口無法操作？
A: 
1. 確認窗口事件處理函數正確
2. 檢查 other.CallFunction 調用是否正確
3. 驗證 familypool.lua 函數是否存在

### Q: 家族變數獲取失敗？
A: 
1. 確認使用 "家族索引" 而不是 "家族編號"
2. 檢查 mylua/charbase.c 中的變數名稱
3. 驗證玩家是否已加入家族

## 版本信息
- 修正版本: 1.4
- 修正日期: 2025-06-18
- 修正內容: 
  - 添加了正確的 NPC 創建和函數指針設置
  - 整合了窗口事件處理到主系統
  - 修正了跨文件函數調用方式
  - 使用正確的家族變數名稱和道具操作函數
  - 修正了 lssproto.windows 的對象索引參數
  - 移至正確目錄並使用 Big5 編碼
- 兼容性: mylua 系統標準

## 重要提醒

### ✅ 已修正的關鍵問題
1. **NPC 創建**: family_system.lua 現在會正確創建 NPC
2. **事件處理**: 窗口事件由主系統統一處理
3. **函數調用**: 使用正確的 other.CallFunction 方式
4. **編碼問題**: 使用 Big5 編碼確保中文顯示

### 🚨 部署注意事項
1. 確保兩個文件都使用 Big5 編碼
2. 先載入 familypool.lua 再載入 family_system.lua
3. 檢查 NPC 座標是否適合你的服務器
4. 根據實際遊戲調整道具編號

現在系統已經可以正常部署和使用了！
