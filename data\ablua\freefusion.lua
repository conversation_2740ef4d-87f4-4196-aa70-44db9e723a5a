function getIntPart(x)
    if x <= 0 then
       return math.ceil(x);
    end

    if math.ceil(x) == x then
       x = math.ceil(x);
    else
       x = math.ceil(x) - 1;
    end
    return x;
end


function FreeFusionSkill( petskillid )
	if skill_list[petskillid]~= nil then
		return 1
	end
	return 0
end

function data()
	skill_list = {
					[41]=1,
					[52]=1,
					[600]=1,
					[601]=1,
					[602]=1,
					[603]=1,
					[604]=1,
					[614]=1,
					[617]=1,
					[628]=1,
					[630]=1,
					[631]=1,
					[635]=1,
					[638]=1,
					[641]=1,
					[593]=1,
					[902]=1,
					[904]=1,
					[906]=1,
					[908]=1,
					[910]=1,
					[912]=1,
					[914]=1,
					[916]=1,
					[918]=1,
					[920]=1,
					[922]=1,
					[924]=1,
					[926]=1,
					[928]=1,
					[930]=1,
					[932]=1,
					[934]=1,
					[936]=1,
					[938]=1,
					[940]=1,
					[943]=1,
					[946]=1,
					[948]=1,
					[950]=1,
					[952]=1,
					[954]=1,
					[956]=1,
					[958]=1,
					[960]=1,
					[962]=1,
					[965]=1,
					[970]=1,
					[973]=1,
					[975]=1,
					[976]=1,
					[978]=1,
					[980]=1,
					[987]=1,
					[988]=1,
					[989]=1,
					[990]=1,
					[991]=1,
					[992]=1,
					[993]=1,
					[994]=1,
					[999]=1,
					[899]=1,
					[1006]=1,
					[1007]=1,
					[1008]=1,
					[1015]=1,
					[1017]=1,
					[1021]=1,
					[1028]=1,
					[1030]=1,
					[1032]=1,
					[1036]=1,
					[1054]=1,
					[1055]=1,
					[1056]=1,
					[1057]=1,
					[1058]=1,
					[1060]=1,
					[1066]=1,
					[1071]=1,
					[1073]=1,
					[1077]=1,
					[1078]=1,
					[1079]=1,
					[1080]=1,
					[1081]=1,
					[1091]=1,
					[1093]=1,
					[1094]=1,
					[1097]=1,
					[1099]=1,
					[672]=1,
					[1117]=1,
					[1128]=1,
					[1131]=1,
					[1132]=1,
					[1150]=1,
					[1151]=1,
					[1152]=1,
					[1153]=1,
					[1154]=1,
					[1157]=1,
					[1168]=1,
					[1186]=1,
					[1187]=1,
					[1190]=1,
					[1196]=1,
					[1200]=1,
					[1209]=1,
					[1215]=1,
					[1216]=1,
					[1217]=1,
					[1218]=1,
					[1220]=1,
					[1222]=1,
					[1224]=1,
					[1225]=1,
					[1226]=1,
					[1227]=1,
					[1228]=1,
					[1233]=1,
					[1234]=1,
					[1235]=1,
					[1240]=1,
					[1241]=1,
					[1243]=1,
					[1248]=1,
					[1250]=1,
					[1251]=1,
					[1252]=1,
					[1253]=1,
					[1255]=1,
					[1265]=1,
					[1266]=1,
					[1271]=1,
					[1273]=1,
					[1281]=1,
					[1282]=1,
					[1283]=1,
					[1284]=1,
					[1286]=1,
					[1287]=1,
					[1288]=1,
					[1289]=1,
					[1290]=1,
					[1291]=1,
					[1293]=1,
					[1300]=1,
					[1302]=1,
					[1304]=1,
					[1309]=1,
					[1312]=1,
					[1324]=1,
					[1333]=1,
					[1336]=1,
					[1338]=1,
					[1339]=1,
					[1350]=1,
					[1352]=1,
					[1357]=1,
					[1358]=1,
					[1359]=1,
					[1360]=1,
					[1362]=1,
					[1371]=1,
					[1372]=1,
					[1374]=1,
					[1375]=1,
					[1382]=1,
					[1390]=1,
					[1391]=1,
					[1392]=1,
					[1393]=1,
					[1395]=1,
					[1396]=1,
					[1408]=1,
					[1411]=1,
					[1413]=1,
					[1422]=1,
					[1424]=1,
					[1438]=1,
					[1441]=1,
					[1442]=1,
					[1443]=1,
					[1444]=1,
					[1447]=1,
					[1457]=1,
					[1459]=1,
					[1472]=1,
					[1478]=1,
					[1479]=1,
					[1481]=1,
					[1482]=1,
					[1491]=1,
					[1501]=1,
					[1509]=1,
					[1510]=1,
					[1511]=1,
					[1524]=1,
					[1526]=1,
					[1530]=1,
					[1537]=1,
					[1538]=1,
					[1539]=1,
					[1541]=1,
					[1550]=1,
					[1551]=1,
					[1552]=1,
					[1553]=1,
					[1554]=1,
					[1560]=1,
					[1564]=1,
					[1577]=1,
					[1578]=1,
					[1589]=1,
					[1591]=1,
					[1592]=1,
					[1600]=1,
					[1601]=1,
					[1611]=1,
					[1612]=1,
					[1617]=1,
					[1618]=1,
					[1619]=1,
					[1646]=1,
					[1649]=1,
					[1650]=1,
					[1654]=1,
					[1656]=1,
					[1657]=1,
					[1658]=1,
					[1659]=1,
					[1673]=1,
					[1675]=1,
					[1676]=1,
					[1677]=1,
					[1699]=1,
					[1702]=1,
					[1703]=1,
					[1704]=1,
					[1705]=1,
					[1706]=1,
					[1706]=1,
					[1708]=1,
					[1709]=1,
					[1719]=1,
					[1720]=1,
					[1722]=1,
					[1723]=1,
					[1724]=1,
					[1725]=1,
					[1726]=1,
					[1739]=1,
					[1742]=1,
					[1743]=1,
					[1757]=1,
					[1758]=1,
					[1759]=1,
					[1760]=1,
					[1761]=1,
					[1762]=1,
					[1778]=1,
					[1779]=1,
					[1790]=1,
					[1793]=1,
					[1838]=1,
					[1839]=1,
					[1840]=1,
					[1841]=1,
					[1842]=1,
					[1843]=1,
					[1853]=1,
					[1855]=1,
					[1867]=1,
					[1873]=1,
					[1875]=1,
					[1876]=1,
					[1877]=1,
					[1878]=1,
					[1879]=1,
					[1888]=1,
					[1890]=1,
					[1891]=1,
					[1893]=1,
					[1905]=1,
					[1906]=1,
					[1907]=1,
					[1910]=1,
					[1911]=1,
					[1912]=1,
					[1913]=1,
					[1914]=1,
					[1928]=1,
					[1929]=1,
					[1932]=1,
					[1938]=1,
					[1939]=1,
					[1951]=1,
					[1952]=1,
					[1963]=1,
					[1970]=1,
					[1971]=1,
					[1993]=1,
					[1994]=1,
				}
end

function main()
	data()
end

